"""
HomeCraft Intelligence - Safety Service

This module handles safety compliance checking, building code validation,
and safety warning generation for DIY home improvement projects.

Author: HomeCraft Intelligence Team
License: MIT
"""

import logging
import re
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import aiohttp
import json

from app.config import settings
from app.core.exceptions import SafetyError, ComplianceError


class SafetyService:
    """
    Service for safety compliance and building code validation.
    
    This service provides:
    - Safety hazard detection in project descriptions
    - Building code compliance checking
    - Safety warning generation
    - Risk assessment and scoring
    - Permit requirement identification
    """
    
    def __init__(self):
        self.safety_threshold = settings.SAFETY_SCORE_THRESHOLD
        self.require_safety_check = settings.REQUIRE_SAFETY_CHECK
        self.session = None
        
        # Safety keywords and their risk levels
        self.safety_keywords = {
            'electrical': {
                'high_risk': ['electrical panel', 'circuit breaker', 'main electrical', 'high voltage', '220v', '240v'],
                'medium_risk': ['outlet', 'switch', 'wiring', 'electrical', 'circuit', 'breaker'],
                'low_risk': ['light fixture', 'lamp', 'led', 'bulb']
            },
            'plumbing': {
                'high_risk': ['gas line', 'main water line', 'sewer line', 'water heater'],
                'medium_risk': ['plumbing', 'pipe', 'faucet', 'toilet', 'drain'],
                'low_risk': ['sink', 'shower head', 'faucet aerator']
            },
            'structural': {
                'high_risk': ['load bearing', 'foundation', 'structural beam', 'support beam', 'joist'],
                'medium_risk': ['wall removal', 'framing', 'drywall', 'insulation'],
                'low_risk': ['paint', 'trim', 'molding', 'cabinet']
            },
            'roofing': {
                'high_risk': ['roof repair', 'shingle replacement', 'roof structure', 'chimney'],
                'medium_risk': ['gutter', 'downspout', 'roof cleaning'],
                'low_risk': ['roof inspection', 'gutter cleaning']
            },
            'hvac': {
                'high_risk': ['furnace', 'air conditioner installation', 'ductwork', 'gas furnace'],
                'medium_risk': ['hvac', 'air conditioning', 'heating', 'ventilation'],
                'low_risk': ['air filter', 'thermostat', 'vent cleaning']
            }
        }
        
        # Building code requirements by project type
        self.code_requirements = {
            'electrical': {
                'permit_required': True,
                'professional_required': True,
                'inspection_required': True,
                'codes': ['NEC', 'Local Electrical Code']
            },
            'plumbing': {
                'permit_required': True,
                'professional_required': True,
                'inspection_required': True,
                'codes': ['IPC', 'Local Plumbing Code']
            },
            'structural': {
                'permit_required': True,
                'professional_required': True,
                'inspection_required': True,
                'codes': ['IBC', 'Local Building Code']
            },
            'roofing': {
                'permit_required': False,
                'professional_required': False,
                'inspection_required': False,
                'codes': ['IBC', 'Local Building Code']
            },
            'hvac': {
                'permit_required': True,
                'professional_required': True,
                'inspection_required': True,
                'codes': ['IMC', 'Local Mechanical Code']
            }
        }
    
    async def initialize(self) -> None:
        """Initialize the safety service."""
        try:
            # Create HTTP session for external API calls
            timeout = aiohttp.ClientTimeout(total=30, connect=10)
            self.session = aiohttp.ClientSession(timeout=timeout)
            
            logging.info("Safety service initialized successfully")
            
        except Exception as e:
            logging.error(f"Failed to initialize safety service: {e}")
            raise SafetyError(f"Initialization failed: {str(e)}")
    
    async def assess_project_safety(
        self,
        project_description: str,
        project_type: str,
        user_skill_level: str,
        location: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Assess the safety of a project based on description and context.
        
        Args:
            project_description: Description of the project
            project_type: Type of project (electrical, plumbing, etc.)
            user_skill_level: User's skill level (beginner, intermediate, advanced)
            location: User's location for local code requirements
            
        Returns:
            Safety assessment with score, warnings, and recommendations
        """
        try:
            # Analyze project for safety hazards
            hazards = await self._identify_hazards(project_description, project_type)
            
            # Calculate risk score
            risk_score = await self._calculate_risk_score(
                hazards, project_type, user_skill_level
            )
            
            # Generate safety warnings
            warnings = await self._generate_safety_warnings(
                hazards, project_type, user_skill_level
            )
            
            # Check building code requirements
            code_requirements = await self._check_building_codes(
                project_type, location
            )
            
            # Generate recommendations
            recommendations = await self._generate_recommendations(
                hazards, risk_score, user_skill_level, code_requirements
            )
            
            # Calculate overall safety score (0-100)
            safety_score = max(0, 100 - (risk_score * 20))
            
            return {
                'safety_score': safety_score,
                'risk_level': self._get_risk_level(safety_score),
                'hazards_identified': hazards,
                'safety_warnings': warnings,
                'code_requirements': code_requirements,
                'recommendations': recommendations,
                'professional_required': code_requirements.get('professional_required', False),
                'permit_required': code_requirements.get('permit_required', False),
                'assessment_timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logging.error(f"Safety assessment failed: {e}")
            raise SafetyError(f"Safety assessment failed: {str(e)}")
    
    async def _identify_hazards(
        self,
        description: str,
        project_type: str
    ) -> List[Dict[str, Any]]:
        """Identify potential hazards in the project description."""
        hazards = []
        description_lower = description.lower()
        
        # Check for project-specific hazards
        if project_type in self.safety_keywords:
            keywords = self.safety_keywords[project_type]
            
            for risk_level, keyword_list in keywords.items():
                for keyword in keyword_list:
                    if keyword in description_lower:
                        hazards.append({
                            'type': project_type,
                            'keyword': keyword,
                            'risk_level': risk_level,
                            'description': f"Potential {risk_level.replace('_', ' ')} hazard: {keyword}"
                        })
        
        # Check for general safety hazards
        general_hazards = {
            'height': ['ladder', 'roof', 'ceiling', 'second floor', 'attic'],
            'power_tools': ['saw', 'drill', 'grinder', 'nail gun', 'power tool'],
            'chemicals': ['paint', 'solvent', 'adhesive', 'cleaner', 'chemical'],
            'fire': ['torch', 'welding', 'heat gun', 'flame', 'fire'],
            'confined_space': ['crawl space', 'basement', 'attic', 'tight space']
        }
        
        for hazard_type, keywords in general_hazards.items():
            for keyword in keywords:
                if keyword in description_lower:
                    hazards.append({
                        'type': hazard_type,
                        'keyword': keyword,
                        'risk_level': 'medium_risk',
                        'description': f"General safety concern: {hazard_type.replace('_', ' ')}"
                    })
        
        return hazards
    
    async def _calculate_risk_score(
        self,
        hazards: List[Dict[str, Any]],
        project_type: str,
        user_skill_level: str
    ) -> float:
        """Calculate overall risk score (0-5 scale)."""
        base_score = 0
        
        # Add points for each hazard
        for hazard in hazards:
            if hazard['risk_level'] == 'high_risk':
                base_score += 2.0
            elif hazard['risk_level'] == 'medium_risk':
                base_score += 1.0
            else:
                base_score += 0.5
        
        # Adjust for user skill level
        skill_multipliers = {
            'beginner': 1.5,
            'intermediate': 1.0,
            'advanced': 0.7,
            'professional': 0.5
        }
        
        skill_multiplier = skill_multipliers.get(user_skill_level, 1.0)
        adjusted_score = base_score * skill_multiplier
        
        # Cap at maximum risk score
        return min(adjusted_score, 5.0)
    
    async def _generate_safety_warnings(
        self,
        hazards: List[Dict[str, Any]],
        project_type: str,
        user_skill_level: str
    ) -> List[str]:
        """Generate specific safety warnings based on identified hazards."""
        warnings = []
        
        # Project-specific warnings
        project_warnings = {
            'electrical': [
                "Turn off power at the circuit breaker before starting work",
                "Use a non-contact voltage tester to verify power is off",
                "Never work on electrical systems in wet conditions",
                "Consider hiring a licensed electrician for complex work"
            ],
            'plumbing': [
                "Shut off water supply before starting work",
                "Have towels and buckets ready for water spillage",
                "Check local codes for pipe materials and installation methods",
                "Consider professional help for gas line work"
            ],
            'structural': [
                "Never remove walls without checking if they're load-bearing",
                "Obtain proper permits before structural modifications",
                "Use appropriate safety equipment and fall protection",
                "Consult a structural engineer for major changes"
            ],
            'roofing': [
                "Use proper fall protection equipment",
                "Work only in good weather conditions",
                "Have someone spot you when using ladders",
                "Inspect roof structure before walking on it"
            ],
            'hvac': [
                "Turn off power and gas before working on HVAC systems",
                "Ensure proper ventilation when working with refrigerants",
                "Follow manufacturer's installation guidelines",
                "Consider professional installation for gas appliances"
            ]
        }
        
        if project_type in project_warnings:
            warnings.extend(project_warnings[project_type])
        
        # Hazard-specific warnings
        for hazard in hazards:
            if hazard['risk_level'] == 'high_risk':
                warnings.append(f"HIGH RISK: {hazard['description']} - Consider professional help")
            elif hazard['risk_level'] == 'medium_risk':
                warnings.append(f"CAUTION: {hazard['description']} - Take proper safety precautions")
        
        # Skill-level specific warnings
        if user_skill_level == 'beginner':
            warnings.extend([
                "Take your time and don't rush the project",
                "Read all instructions thoroughly before starting",
                "Have a more experienced person review your work",
                "Don't hesitate to call a professional if you're unsure"
            ])
        
        return list(set(warnings))  # Remove duplicates
    
    async def _check_building_codes(
        self,
        project_type: str,
        location: Optional[str] = None
    ) -> Dict[str, Any]:
        """Check building code requirements for the project."""
        try:
            # Get base requirements
            requirements = self.code_requirements.get(project_type, {
                'permit_required': False,
                'professional_required': False,
                'inspection_required': False,
                'codes': ['Local Building Code']
            })
            
            # Add location-specific requirements if available
            if location and settings.BUILDING_CODES_API_KEY:
                location_requirements = await self._fetch_local_codes(location, project_type)
                if location_requirements:
                    requirements.update(location_requirements)
            
            return requirements
            
        except Exception as e:
            logging.warning(f"Building code check failed: {e}")
            return self.code_requirements.get(project_type, {})
    
    async def _fetch_local_codes(
        self,
        location: str,
        project_type: str
    ) -> Optional[Dict[str, Any]]:
        """Fetch local building codes from external API."""
        try:
            if not settings.BUILDING_CODES_API_URL or not settings.BUILDING_CODES_API_KEY:
                return None
            
            headers = {
                "Authorization": f"Bearer {settings.BUILDING_CODES_API_KEY}",
                "Content-Type": "application/json"
            }
            
            params = {
                "location": location,
                "project_type": project_type
            }
            
            url = f"{settings.BUILDING_CODES_API_URL}/codes"
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logging.warning(f"Building codes API returned {response.status}")
                    return None
                    
        except Exception as e:
            logging.warning(f"Failed to fetch local codes: {e}")
            return None
    
    async def _generate_recommendations(
        self,
        hazards: List[Dict[str, Any]],
        risk_score: float,
        user_skill_level: str,
        code_requirements: Dict[str, Any]
    ) -> List[str]:
        """Generate safety recommendations based on assessment."""
        recommendations = []
        
        # Risk-based recommendations
        if risk_score >= 4.0:
            recommendations.append("STRONGLY RECOMMEND hiring a professional for this project")
            recommendations.append("This project involves significant safety risks")
        elif risk_score >= 2.5:
            recommendations.append("Consider consulting with a professional before starting")
            recommendations.append("Ensure you have proper safety equipment and training")
        elif risk_score >= 1.0:
            recommendations.append("Review safety procedures carefully before starting")
            recommendations.append("Have safety equipment readily available")
        
        # Code-based recommendations
        if code_requirements.get('permit_required'):
            recommendations.append("Obtain required building permits before starting work")
        
        if code_requirements.get('professional_required'):
            recommendations.append("This work requires a licensed professional")
        
        if code_requirements.get('inspection_required'):
            recommendations.append("Schedule required inspections during the project")
        
        # Skill-based recommendations
        if user_skill_level == 'beginner' and risk_score > 1.0:
            recommendations.extend([
                "Consider taking a safety course before starting",
                "Practice with simpler projects first",
                "Have an experienced person supervise the work"
            ])
        
        return recommendations
    
    def _get_risk_level(self, safety_score: float) -> str:
        """Convert safety score to risk level."""
        if safety_score >= 80:
            return "low"
        elif safety_score >= 60:
            return "medium"
        elif safety_score >= 40:
            return "high"
        else:
            return "very_high"
    
    async def get_safety_warnings(
        self,
        query: str,
        response: str,
        context: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """Get safety warnings for a specific query and response."""
        try:
            warnings = []
            
            # Analyze query and response for safety concerns
            combined_text = f"{query} {response}".lower()
            
            # Check for dangerous activities
            dangerous_patterns = [
                r'without\s+(safety|protection|equipment)',
                r'skip\s+(safety|precaution)',
                r'ignore\s+(warning|safety)',
                r'shortcut\s+(method|way)',
                r'quick\s+fix'
            ]
            
            for pattern in dangerous_patterns:
                if re.search(pattern, combined_text):
                    warnings.append("Always prioritize safety over speed or convenience")
                    break
            
            # Add context-specific warnings
            if context:
                skill_level = context.get('skill_level', 'beginner')
                if skill_level == 'beginner':
                    warnings.append("As a beginner, take extra time to understand safety procedures")
                
                project_type = context.get('project_type')
                if project_type in ['electrical', 'plumbing', 'structural']:
                    warnings.append(f"Consider consulting a professional for {project_type} work")
            
            return warnings
            
        except Exception as e:
            logging.error(f"Safety warning generation failed: {e}")
            return ["Always prioritize safety and consult professionals when in doubt"]
    
    async def cleanup(self) -> None:
        """Clean up resources."""
        try:
            if self.session:
                await self.session.close()
            
            logging.info("Safety service cleanup completed")
            
        except Exception as e:
            logging.error(f"Safety service cleanup failed: {e}")


# Global safety service instance
safety_service = SafetyService()
