"""
HomeCraft Intelligence - Rate Limiting Middleware

This module implements comprehensive rate limiting for API endpoints
with Redis backend, user-based limits, and endpoint-specific configurations.

Author: HomeCraft Intelligence Team
License: MIT
"""

import logging
import time
from typing import Dict, Optional, Callable, Any
from fastapi import Request, Response, HTTPException, status
from fastapi.responses import JSONResponse
import redis.asyncio as redis
import json
from datetime import datetime, timedelta

from app.config import settings
from app.core.exceptions import RateLimitError


class RateLimiter:
    """
    Advanced rate limiter with Redis backend supporting multiple strategies.
    
    Features:
    - Per-user and per-IP rate limiting
    - Endpoint-specific limits
    - Sliding window algorithm
    - Premium user exemptions
    - Burst allowance
    - Rate limit headers
    """
    
    def __init__(self):
        self.redis_client = None
        self.default_limits = {
            'requests_per_minute': settings.RATE_LIMIT_REQUESTS,
            'requests_per_hour': settings.RATE_LIMIT_REQUESTS * 60,
            'requests_per_day': settings.RATE_LIMIT_REQUESTS * 60 * 24
        }
        
        # Endpoint-specific rate limits
        self.endpoint_limits = {
            '/api/v1/auth/login': {
                'requests_per_minute': 5,
                'requests_per_hour': 20,
                'burst_allowance': 2
            },
            '/api/v1/auth/register': {
                'requests_per_minute': 3,
                'requests_per_hour': 10,
                'burst_allowance': 1
            },
            '/api/v1/rag/query': {
                'requests_per_minute': 30,
                'requests_per_hour': 500,
                'burst_allowance': 5
            },
            '/api/v1/materials/calculate': {
                'requests_per_minute': 20,
                'requests_per_hour': 200,
                'burst_allowance': 3
            },
            '/api/v1/safety/assess': {
                'requests_per_minute': 15,
                'requests_per_hour': 150,
                'burst_allowance': 2
            }
        }
        
        # Premium user multipliers
        self.premium_multipliers = {
            'basic': 1.0,
            'premium': 3.0,
            'professional': 5.0,
            'enterprise': 10.0
        }
    
    async def initialize(self):
        """Initialize Redis connection for rate limiting."""
        try:
            self.redis_client = redis.from_url(
                settings.REDIS_URL,
                encoding="utf-8",
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5
            )
            
            # Test connection
            await self.redis_client.ping()
            logging.info("Rate limiter Redis connection established")
            
        except Exception as e:
            logging.error(f"Failed to initialize rate limiter Redis: {e}")
            # Fallback to in-memory rate limiting
            self.redis_client = None
    
    async def is_allowed(
        self,
        request: Request,
        endpoint: str,
        user_id: Optional[str] = None,
        user_tier: str = 'basic'
    ) -> Dict[str, Any]:
        """
        Check if request is allowed based on rate limits.
        
        Args:
            request: FastAPI request object
            endpoint: API endpoint path
            user_id: User ID if authenticated
            user_tier: User subscription tier
            
        Returns:
            Dict with allowed status and limit info
        """
        try:
            # Get client identifier
            client_id = self._get_client_id(request, user_id)
            
            # Get rate limits for endpoint
            limits = self._get_endpoint_limits(endpoint, user_tier)
            
            # Check each time window
            current_time = time.time()
            results = {}
            
            for window, limit in limits.items():
                if window == 'burst_allowance':
                    continue
                    
                window_seconds = self._get_window_seconds(window)
                key = f"rate_limit:{client_id}:{endpoint}:{window}"
                
                # Check current usage
                current_usage = await self._get_current_usage(
                    key, window_seconds, current_time
                )
                
                results[window] = {
                    'limit': limit,
                    'used': current_usage,
                    'remaining': max(0, limit - current_usage),
                    'reset_time': current_time + window_seconds
                }
                
                # If any window is exceeded, deny request
                if current_usage >= limit:
                    return {
                        'allowed': False,
                        'limits': results,
                        'retry_after': self._calculate_retry_after(
                            key, window_seconds, current_time
                        )
                    }
            
            # Request is allowed, increment counters
            await self._increment_usage(client_id, endpoint, limits, current_time)
            
            return {
                'allowed': True,
                'limits': results,
                'retry_after': None
            }
            
        except Exception as e:
            logging.error(f"Rate limit check failed: {e}")
            # Allow request on error to avoid blocking legitimate traffic
            return {'allowed': True, 'limits': {}, 'retry_after': None}
    
    def _get_client_id(self, request: Request, user_id: Optional[str]) -> str:
        """Get unique client identifier for rate limiting."""
        if user_id:
            return f"user:{user_id}"
        
        # Use IP address as fallback
        client_ip = self._get_client_ip(request)
        return f"ip:{client_ip}"
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address from request."""
        # Check for forwarded headers (load balancer/proxy)
        forwarded_for = request.headers.get('X-Forwarded-For')
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()
        
        real_ip = request.headers.get('X-Real-IP')
        if real_ip:
            return real_ip
        
        # Fallback to direct connection
        return request.client.host if request.client else '127.0.0.1'
    
    def _get_endpoint_limits(self, endpoint: str, user_tier: str) -> Dict[str, int]:
        """Get rate limits for specific endpoint and user tier."""
        # Get base limits for endpoint
        base_limits = self.endpoint_limits.get(endpoint, self.default_limits)
        
        # Apply user tier multiplier
        multiplier = self.premium_multipliers.get(user_tier, 1.0)
        
        adjusted_limits = {}
        for window, limit in base_limits.items():
            if window != 'burst_allowance':
                adjusted_limits[window] = int(limit * multiplier)
            else:
                adjusted_limits[window] = limit
        
        return adjusted_limits
    
    def _get_window_seconds(self, window: str) -> int:
        """Convert window name to seconds."""
        window_map = {
            'requests_per_minute': 60,
            'requests_per_hour': 3600,
            'requests_per_day': 86400
        }
        return window_map.get(window, 60)
    
    async def _get_current_usage(
        self,
        key: str,
        window_seconds: int,
        current_time: float
    ) -> int:
        """Get current usage count for time window."""
        if not self.redis_client:
            return 0
        
        try:
            # Use sliding window log approach
            window_start = current_time - window_seconds
            
            # Remove expired entries
            await self.redis_client.zremrangebyscore(key, 0, window_start)
            
            # Count current entries
            count = await self.redis_client.zcard(key)
            
            return count
            
        except Exception as e:
            logging.error(f"Failed to get current usage: {e}")
            return 0
    
    async def _increment_usage(
        self,
        client_id: str,
        endpoint: str,
        limits: Dict[str, int],
        current_time: float
    ):
        """Increment usage counters for all time windows."""
        if not self.redis_client:
            return
        
        try:
            # Create pipeline for atomic operations
            pipe = self.redis_client.pipeline()
            
            for window in limits.keys():
                if window == 'burst_allowance':
                    continue
                    
                window_seconds = self._get_window_seconds(window)
                key = f"rate_limit:{client_id}:{endpoint}:{window}"
                
                # Add current request to sorted set
                pipe.zadd(key, {str(current_time): current_time})
                
                # Set expiration for cleanup
                pipe.expire(key, window_seconds + 60)  # Extra buffer
            
            await pipe.execute()
            
        except Exception as e:
            logging.error(f"Failed to increment usage: {e}")
    
    async def _calculate_retry_after(
        self,
        key: str,
        window_seconds: int,
        current_time: float
    ) -> int:
        """Calculate seconds until rate limit resets."""
        try:
            if not self.redis_client:
                return window_seconds
            
            # Get oldest entry in current window
            oldest_entries = await self.redis_client.zrange(key, 0, 0, withscores=True)
            
            if oldest_entries:
                oldest_time = oldest_entries[0][1]
                retry_after = int(oldest_time + window_seconds - current_time)
                return max(1, retry_after)
            
            return window_seconds
            
        except Exception as e:
            logging.error(f"Failed to calculate retry after: {e}")
            return window_seconds
    
    def create_rate_limit_headers(self, limits: Dict[str, Any]) -> Dict[str, str]:
        """Create rate limit headers for response."""
        headers = {}
        
        # Use the most restrictive limit for headers
        if 'requests_per_minute' in limits:
            minute_limit = limits['requests_per_minute']
            headers.update({
                'X-RateLimit-Limit': str(minute_limit['limit']),
                'X-RateLimit-Remaining': str(minute_limit['remaining']),
                'X-RateLimit-Reset': str(int(minute_limit['reset_time']))
            })
        
        return headers


# Global rate limiter instance
rate_limiter = RateLimiter()


async def rate_limit_middleware(request: Request, call_next: Callable) -> Response:
    """
    Rate limiting middleware for FastAPI.
    
    Applies rate limits based on endpoint, user, and subscription tier.
    """
    try:
        # Skip rate limiting for health checks and static files
        if request.url.path in ['/health', '/metrics', '/favicon.ico']:
            return await call_next(request)
        
        # Extract user information from request
        user_id = None
        user_tier = 'basic'
        
        # Try to get user from authorization header
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            # This would typically decode JWT and extract user info
            # For now, we'll use a placeholder
            user_id = getattr(request.state, 'user_id', None)
            user_tier = getattr(request.state, 'user_tier', 'basic')
        
        # Check rate limits
        endpoint = request.url.path
        limit_result = await rate_limiter.is_allowed(
            request, endpoint, user_id, user_tier
        )
        
        if not limit_result['allowed']:
            # Create rate limit error response
            headers = rate_limiter.create_rate_limit_headers(limit_result['limits'])
            headers['Retry-After'] = str(limit_result['retry_after'])
            
            return JSONResponse(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                content={
                    'error_code': 'RATE_LIMIT_EXCEEDED',
                    'message': 'Rate limit exceeded. Please try again later.',
                    'retry_after': limit_result['retry_after'],
                    'limits': limit_result['limits']
                },
                headers=headers
            )
        
        # Process request
        response = await call_next(request)
        
        # Add rate limit headers to response
        if limit_result['limits']:
            headers = rate_limiter.create_rate_limit_headers(limit_result['limits'])
            for key, value in headers.items():
                response.headers[key] = value
        
        return response
        
    except Exception as e:
        logging.error(f"Rate limiting middleware error: {e}")
        # Continue processing request on middleware error
        return await call_next(request)


# Decorator for endpoint-specific rate limiting
def rate_limit(
    requests_per_minute: int = None,
    requests_per_hour: int = None,
    burst_allowance: int = None
):
    """
    Decorator for applying custom rate limits to specific endpoints.
    
    Args:
        requests_per_minute: Requests allowed per minute
        requests_per_hour: Requests allowed per hour
        burst_allowance: Additional requests allowed in burst
    """
    def decorator(func):
        # Store rate limit configuration on function
        func._rate_limits = {
            'requests_per_minute': requests_per_minute,
            'requests_per_hour': requests_per_hour,
            'burst_allowance': burst_allowance
        }
        return func
    return decorator
