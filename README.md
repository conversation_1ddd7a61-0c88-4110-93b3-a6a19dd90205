# HomeCraft Intelligence 🏠🔧

> **AI-Powered DIY Home Improvement Assistant**  
> Your intelligent companion for safe, efficient, and successful home improvement projects.

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python 3.11+](https://img.shields.io/badge/python-3.11+-blue.svg)](https://www.python.org/downloads/)
[![Next.js 14](https://img.shields.io/badge/Next.js-14-black.svg)](https://nextjs.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com/)

## 🎯 Problem Statement

DIY home improvement projects often fail due to:
- **Lack of personalized guidance** based on skill level and project complexity
- **Safety oversights** leading to accidents and code violations
- **Poor material planning** resulting in cost overruns and delays
- **Fragmented information** scattered across multiple sources
- **No progress tracking** or community support system

## 🚀 Solution Overview

HomeCraft Intelligence is a comprehensive RAG-powered assistant that provides:

### ✨ Core Features

- **🤖 Intelligent Project Planning** - AI-driven project breakdown with personalized recommendations
- **🛡️ Safety-First Approach** - Real-time safety compliance checking and building code integration
- **📊 Smart Material Calculator** - Accurate cost estimation and material optimization
- **🎓 Skill-Based Recommendations** - Personalized tool and technique suggestions
- **📱 Progress Tracking** - Visual project documentation and milestone tracking
- **👥 Community Knowledge Sharing** - Learn from experienced DIYers and professionals
- **🔍 Intelligent Search** - RAG-powered knowledge base with contextual answers
- **🌦️ Environmental Considerations** - Weather-aware project scheduling

### 🎯 Target Users

- **DIY Enthusiasts** - Weekend warriors tackling home projects
- **First-Time Homeowners** - Learning basic maintenance and improvements
- **Professional Contractors** - Seeking efficient project planning tools
- **Home Improvement Stores** - Providing enhanced customer support

## 🏗️ System Architecture

### Technology Stack

**Backend (Python)**
- **FastAPI** - High-performance API framework
- **PostgreSQL + pgvector** - Relational database with vector search
- **Chroma** - Vector database for RAG implementation
- **Sentence Transformers** - Open-source embedding models
- **Ollama** - Local LLM deployment for privacy

**Frontend (TypeScript)**
- **Next.js 14** - React framework with App Router
- **Tailwind CSS** - Utility-first styling
- **Zustand** - Lightweight state management
- **NextAuth.js** - Authentication solution

**AI/ML Pipeline**
- **RAG Architecture** - Retrieval-Augmented Generation
- **Vector Search** - Semantic similarity matching
- **Safety Validation** - AI-powered compliance checking
- **Personalization Engine** - Skill-based recommendations

### Architecture Diagram

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI[Next.js Frontend]
        PWA[Progressive Web App]
        Mobile[Mobile Responsive]
    end
    
    subgraph "API Gateway"
        Gateway[FastAPI Gateway]
        Auth[Authentication Service]
        Rate[Rate Limiting]
    end
    
    subgraph "Core Services"
        ProjectAPI[Project Management API]
        RAGAPI[RAG Service API]
        SafetyAPI[Safety Compliance API]
        MaterialAPI[Material Calculator API]
        UserAPI[User Management API]
    end
    
    subgraph "AI/ML Layer"
        Embeddings[Sentence Transformers]
        LLM[Ollama/Local LLM]
        VectorDB[Chroma Vector DB]
        Retrieval[RAG Pipeline]
    end
    
    subgraph "Data Layer"
        PostgresDB[(PostgreSQL + pgvector)]
        KnowledgeBase[(Knowledge Base)]
        UserData[(User Projects)]
        MaterialDB[(Materials Database)]
    end
    
    UI --> Gateway
    Gateway --> ProjectAPI
    Gateway --> RAGAPI
    RAGAPI --> Retrieval
    Retrieval --> VectorDB
    ProjectAPI --> PostgresDB
```

## 🚀 Quick Start

### Prerequisites

- **Docker & Docker Compose** (recommended)
- **Python 3.11+** (for local development)
- **Node.js 18+** (for frontend development)
- **PostgreSQL 15+** (if running locally)

### 🐳 Docker Setup (Recommended)

```bash
# Clone the repository
git clone https://github.com/HectorTa1989/homecraft-intelligence.git
cd homecraft-intelligence

# Copy environment variables
cp .env.example .env
# Edit .env with your configuration

# Start all services
docker-compose up -d

# Initialize the database
docker-compose exec backend python scripts/init_db.py

# Seed knowledge base
docker-compose exec backend python scripts/seed_data.py
```

### 🛠️ Local Development Setup

```bash
# Backend setup
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt

# Database setup
createdb homecraft_intelligence
alembic upgrade head

# Start backend
uvicorn app.main:app --reload --port 8000

# Frontend setup (new terminal)
cd frontend
npm install
npm run dev
```

### 🌐 Access the Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Admin Panel**: http://localhost:3000/admin

## 📖 User Workflow

### 1. **Onboarding & Skill Assessment**
- Create account and complete profile
- Take skill assessment quiz
- Set safety preferences and local building codes

### 2. **Project Planning**
- Describe your project goals
- Get AI-generated project breakdown
- Review safety requirements and permits needed
- Calculate materials and costs

### 3. **Guided Execution**
- Follow step-by-step instructions
- Track progress with photos and notes
- Get real-time safety reminders
- Ask questions to the AI assistant

### 4. **Community Sharing**
- Document completed projects
- Share tips and lessons learned
- Help other community members
- Build your DIY reputation

## 🔧 API Documentation

### Core Endpoints

```python
# Authentication
POST /api/v1/auth/register
POST /api/v1/auth/login
POST /api/v1/auth/refresh

# Projects
GET /api/v1/projects/
POST /api/v1/projects/
GET /api/v1/projects/{project_id}
PUT /api/v1/projects/{project_id}

# RAG Assistant
POST /api/v1/rag/query
GET /api/v1/rag/suggestions
POST /api/v1/rag/feedback

# Materials
POST /api/v1/materials/calculate
GET /api/v1/materials/prices
GET /api/v1/materials/alternatives

# Safety
POST /api/v1/safety/check
GET /api/v1/safety/codes/{location}
POST /api/v1/safety/report
```

### Example RAG Query

```python
import requests

response = requests.post("http://localhost:8000/api/v1/rag/query", json={
    "query": "How do I safely install a ceiling fan in a room with 8-foot ceilings?",
    "project_context": {
        "skill_level": "beginner",
        "location": "California, USA",
        "room_type": "bedroom"
    }
})

print(response.json())
```

## 🧪 Testing

```bash
# Backend tests
cd backend
pytest -v --cov=app tests/

# Frontend tests
cd frontend
npm test
npm run test:e2e

# Integration tests
docker-compose -f docker-compose.test.yml up --abort-on-container-exit
```

## 📊 Performance Metrics

- **Response Time**: < 200ms for API calls
- **RAG Query Time**: < 2s for complex queries
- **Vector Search**: < 100ms for similarity search
- **Uptime Target**: 99.9%
- **Mobile Performance**: Lighthouse score > 90

## 🛡️ Security Features

- **JWT Authentication** with refresh tokens
- **Rate Limiting** to prevent abuse
- **Input Validation** and sanitization
- **SQL Injection Protection** via ORM
- **XSS Prevention** with CSP headers
- **HTTPS Enforcement** in production
- **Data Encryption** at rest and in transit

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

### Development Workflow

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Commit your changes (`git commit -m 'Add amazing feature'`)
7. Push to the branch (`git push origin feature/amazing-feature`)
8. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Open Source Community** for the amazing tools and libraries
- **DIY Enthusiasts** for inspiration and feedback
- **Safety Organizations** for guidelines and best practices
- **Contributors** who make this project possible

## 📞 Support

- **Documentation**: [docs.homecraft-intelligence.com](https://docs.homecraft-intelligence.com)
- **Issues**: [GitHub Issues](https://github.com/HectorTa1989/homecraft-intelligence/issues)
- **Discussions**: [GitHub Discussions](https://github.com/HectorTa1989/homecraft-intelligence/discussions)
- **Email**: <EMAIL>

---

**Built with ❤️ for the DIY community**
