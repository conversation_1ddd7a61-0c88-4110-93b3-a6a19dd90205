"""
HomeCraft Intelligence - Project Schemas

This module defines Pydantic schemas for project-related API requests and responses,
providing data validation and serialization for the project management system.

Author: HomeCraft Intelligence Team
License: MIT
"""

from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, validator, root_validator
from datetime import datetime
from enum import Enum
import uuid

from app.models.project import ProjectStatus, ProjectType, DifficultyLevel, TaskStatus


# Base schemas
class ProjectDimensions(BaseModel):
    """Schema for project dimensions and measurements."""
    length: Optional[float] = Field(None, ge=0, description="Length in feet")
    width: Optional[float] = Field(None, ge=0, description="Width in feet")
    height: Optional[float] = Field(None, ge=0, description="Height in feet")
    area: Optional[float] = Field(None, ge=0, description="Total area in square feet")
    perimeter: Optional[float] = Field(None, ge=0, description="Perimeter in linear feet")
    volume: Optional[float] = Field(None, ge=0, description="Volume in cubic feet")
    custom_measurements: Optional[Dict[str, float]] = Field(None, description="Custom measurements")
    
    @validator('area')
    def calculate_area_if_missing(cls, v, values):
        """Calculate area from length and width if not provided."""
        if v is None and 'length' in values and 'width' in values:
            length = values.get('length')
            width = values.get('width')
            if length and width:
                return length * width
        return v


class MaterialRequirement(BaseModel):
    """Schema for material requirements in tasks."""
    name: str = Field(..., description="Material name")
    quantity: float = Field(..., ge=0, description="Required quantity")
    unit: str = Field(..., description="Unit of measurement")
    estimated_cost: Optional[float] = Field(None, ge=0, description="Estimated cost per unit")
    is_optional: bool = Field(False, description="Whether material is optional")


# Project schemas
class ProjectBase(BaseModel):
    """Base project schema with common fields."""
    title: str = Field(..., min_length=1, max_length=200, description="Project title")
    description: Optional[str] = Field(None, max_length=2000, description="Project description")
    project_type: ProjectType = Field(..., description="Type of project")
    difficulty_level: DifficultyLevel = Field(..., description="Project difficulty level")
    location: Optional[str] = Field(None, max_length=200, description="Project location/room")
    dimensions: Optional[ProjectDimensions] = Field(None, description="Project dimensions")
    estimated_cost: Optional[float] = Field(None, ge=0, description="Estimated total cost")
    budget_limit: Optional[float] = Field(None, ge=0, description="Budget limit")
    target_completion_date: Optional[datetime] = Field(None, description="Target completion date")
    tags: Optional[List[str]] = Field(None, description="Project tags")
    
    @validator('tags')
    def validate_tags(cls, v):
        """Validate and clean tags."""
        if v:
            # Remove duplicates and empty tags
            cleaned_tags = list(set(tag.strip().lower() for tag in v if tag.strip()))
            return cleaned_tags[:10]  # Limit to 10 tags
        return v


class ProjectCreate(ProjectBase):
    """Schema for creating a new project."""
    pass


class ProjectUpdate(BaseModel):
    """Schema for updating an existing project."""
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=2000)
    project_type: Optional[ProjectType] = None
    difficulty_level: Optional[DifficultyLevel] = None
    location: Optional[str] = Field(None, max_length=200)
    dimensions: Optional[ProjectDimensions] = None
    estimated_cost: Optional[float] = Field(None, ge=0)
    budget_limit: Optional[float] = Field(None, ge=0)
    target_completion_date: Optional[datetime] = None
    status: Optional[ProjectStatus] = None
    progress_percentage: Optional[float] = Field(None, ge=0, le=100)
    actual_cost: Optional[float] = Field(None, ge=0)
    tags: Optional[List[str]] = None
    is_public: Optional[bool] = None


class ProjectResponse(ProjectBase):
    """Schema for project API responses."""
    id: uuid.UUID
    user_id: uuid.UUID
    status: ProjectStatus
    progress_percentage: float
    estimated_duration_hours: Optional[int]
    actual_duration_hours: Optional[int]
    start_date: Optional[datetime]
    actual_completion_date: Optional[datetime]
    actual_cost: float
    cost_breakdown: Optional[Dict[str, Any]]
    safety_score: Optional[float]
    safety_warnings: Optional[List[str]]
    building_permits_required: bool
    professional_help_recommended: bool
    ai_recommendations: Optional[Dict[str, Any]]
    generated_plan: Optional[Dict[str, Any]]
    material_suggestions: Optional[Dict[str, Any]]
    images: Optional[List[str]]
    documents: Optional[List[str]]
    is_public: bool
    is_template: bool
    template_category: Optional[str]
    view_count: int
    like_count: int
    created_at: datetime
    updated_at: datetime
    
    # Computed properties
    is_completed: bool
    is_overdue: bool
    completion_rate: float
    cost_variance: Optional[float]
    cost_variance_percentage: Optional[float]
    
    # Related data
    tasks: Optional[List['ProjectTaskResponse']] = []
    materials: Optional[List['ProjectMaterialResponse']] = []
    progress_logs: Optional[List['ProjectProgressLogResponse']] = []
    
    class Config:
        from_attributes = True


class ProjectListResponse(BaseModel):
    """Schema for paginated project list responses."""
    projects: List[ProjectResponse]
    total: int = Field(..., description="Total number of projects")
    skip: int = Field(..., description="Number of projects skipped")
    limit: int = Field(..., description="Maximum number of projects returned")
    
    @property
    def has_more(self) -> bool:
        """Check if there are more projects available."""
        return self.skip + len(self.projects) < self.total


# Task schemas
class ProjectTaskBase(BaseModel):
    """Base task schema with common fields."""
    title: str = Field(..., min_length=1, max_length=200, description="Task title")
    description: Optional[str] = Field(None, max_length=1000, description="Task description")
    order_index: int = Field(..., ge=0, description="Task order in project")
    estimated_duration_hours: Optional[float] = Field(None, ge=0, description="Estimated duration")
    difficulty_level: Optional[DifficultyLevel] = Field(None, description="Task difficulty")
    required_tools: Optional[List[str]] = Field(None, description="Required tools")
    required_materials: Optional[Dict[str, MaterialRequirement]] = Field(None, description="Required materials")
    safety_notes: Optional[str] = Field(None, max_length=500, description="Safety notes")
    depends_on_task_ids: Optional[List[uuid.UUID]] = Field(None, description="Task dependencies")


class ProjectTaskCreate(ProjectTaskBase):
    """Schema for creating a new project task."""
    pass


class ProjectTaskUpdate(BaseModel):
    """Schema for updating an existing project task."""
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    status: Optional[TaskStatus] = None
    progress_percentage: Optional[float] = Field(None, ge=0, le=100)
    actual_duration_hours: Optional[float] = Field(None, ge=0)
    start_date: Optional[datetime] = None
    completion_date: Optional[datetime] = None
    notes: Optional[str] = Field(None, max_length=1000)


class ProjectTaskResponse(ProjectTaskBase):
    """Schema for project task API responses."""
    id: uuid.UUID
    project_id: uuid.UUID
    status: TaskStatus
    actual_duration_hours: Optional[float]
    start_date: Optional[datetime]
    completion_date: Optional[datetime]
    ai_instructions: Optional[str]
    ai_tips: Optional[List[str]]
    progress_percentage: float
    notes: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    # Computed properties
    is_ready_to_start: bool
    
    class Config:
        from_attributes = True


# Material schemas
class ProjectMaterialBase(BaseModel):
    """Base material schema with common fields."""
    name: str = Field(..., min_length=1, max_length=200, description="Material name")
    description: Optional[str] = Field(None, max_length=500, description="Material description")
    category: Optional[str] = Field(None, max_length=100, description="Material category")
    brand: Optional[str] = Field(None, max_length=100, description="Brand name")
    model_number: Optional[str] = Field(None, max_length=100, description="Model number")
    quantity_needed: float = Field(..., gt=0, description="Quantity needed")
    unit: str = Field(..., min_length=1, max_length=50, description="Unit of measurement")
    estimated_unit_cost: Optional[float] = Field(None, ge=0, description="Estimated cost per unit")
    supplier: Optional[str] = Field(None, max_length=200, description="Supplier name")
    supplier_url: Optional[str] = Field(None, max_length=500, description="Supplier URL")
    specifications: Optional[Dict[str, Any]] = Field(None, description="Material specifications")


class ProjectMaterialCreate(ProjectMaterialBase):
    """Schema for creating a new project material."""
    pass


class ProjectMaterialUpdate(BaseModel):
    """Schema for updating an existing project material."""
    quantity_purchased: Optional[float] = Field(None, ge=0)
    actual_unit_cost: Optional[float] = Field(None, ge=0)
    is_purchased: Optional[bool] = None
    purchase_date: Optional[datetime] = None
    supplier: Optional[str] = Field(None, max_length=200)
    supplier_url: Optional[str] = Field(None, max_length=500)


class ProjectMaterialResponse(ProjectMaterialBase):
    """Schema for project material API responses."""
    id: uuid.UUID
    project_id: uuid.UUID
    quantity_purchased: float
    actual_unit_cost: Optional[float]
    total_estimated_cost: Optional[float]
    total_actual_cost: float
    is_purchased: bool
    purchase_date: Optional[datetime]
    alternatives: Optional[Dict[str, Any]]
    ai_recommended: bool
    ai_reasoning: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    # Computed properties
    quantity_remaining: float
    cost_variance: Optional[float]
    
    class Config:
        from_attributes = True


# Progress log schemas
class ProjectProgressLogBase(BaseModel):
    """Base progress log schema with common fields."""
    title: Optional[str] = Field(None, max_length=200, description="Log entry title")
    description: str = Field(..., min_length=1, max_length=2000, description="Progress description")
    progress_percentage: Optional[float] = Field(None, ge=0, le=100, description="Progress percentage")
    hours_worked: Optional[float] = Field(None, ge=0, description="Hours worked")
    images: Optional[List[str]] = Field(None, description="Image URLs")
    videos: Optional[List[str]] = Field(None, description="Video URLs")
    documents: Optional[List[str]] = Field(None, description="Document URLs")
    tags: Optional[List[str]] = Field(None, description="Log entry tags")
    is_milestone: bool = Field(False, description="Whether this is a milestone")


class ProjectProgressLogCreate(ProjectProgressLogBase):
    """Schema for creating a new progress log entry."""
    pass


class ProjectProgressLogResponse(ProjectProgressLogBase):
    """Schema for progress log API responses."""
    id: uuid.UUID
    project_id: uuid.UUID
    created_at: datetime
    
    class Config:
        from_attributes = True


# Update forward references
ProjectResponse.model_rebuild()
ProjectTaskResponse.model_rebuild()
ProjectMaterialResponse.model_rebuild()
ProjectProgressLogResponse.model_rebuild()
