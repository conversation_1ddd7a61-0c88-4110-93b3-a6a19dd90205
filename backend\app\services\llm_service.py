"""
HomeCraft Intelligence - LLM Service

This module handles Large Language Model interactions for generating responses
in the RAG pipeline. Supports multiple LLM providers including Ollama, OpenAI, and Hugging Face.

Author: HomeCraft Intelligence Team
License: MIT
"""

import logging
import asyncio
import aiohttp
import json
from typing import Dict, Any, Optional, List, AsyncGenerator
from datetime import datetime
import time

from app.config import settings
from app.core.exceptions import LLMError, RateLimitError


class LLMService:
    """
    Service for interacting with Large Language Models.
    
    This service provides:
    - Multi-provider LLM support (Ollama, OpenAI, Hugging Face)
    - Streaming and non-streaming responses
    - Rate limiting and error handling
    - Response caching and optimization
    - Safety filtering and content moderation
    """
    
    def __init__(self):
        self.provider = settings.LLM_PROVIDER
        self.model = settings.LLM_MODEL
        self.temperature = settings.LLM_TEMPERATURE
        self.max_tokens = settings.LLM_MAX_TOKENS
        self.session = None
        self.request_count = 0
        self.last_request_time = 0
        
    async def initialize(self) -> None:
        """Initialize the LLM service."""
        try:
            # Create HTTP session
            timeout = aiohttp.ClientTimeout(total=60, connect=10)
            self.session = aiohttp.ClientSession(timeout=timeout)
            
            # Test connection based on provider
            await self._test_connection()
            
            logging.info(f"LLM service initialized with provider: {self.provider}")
            
        except Exception as e:
            logging.error(f"Failed to initialize LLM service: {e}")
            raise LLMError(f"Initialization failed: {str(e)}")
    
    async def _test_connection(self) -> None:
        """Test connection to the LLM provider."""
        try:
            if self.provider == "ollama":
                await self._test_ollama_connection()
            elif self.provider == "openai":
                await self._test_openai_connection()
            elif self.provider == "huggingface":
                await self._test_huggingface_connection()
            else:
                raise LLMError(f"Unsupported LLM provider: {self.provider}")
                
        except Exception as e:
            raise LLMError(f"Connection test failed: {str(e)}")
    
    async def _test_ollama_connection(self) -> None:
        """Test Ollama connection."""
        url = f"{settings.OLLAMA_BASE_URL}/api/tags"
        async with self.session.get(url) as response:
            if response.status != 200:
                raise LLMError(f"Ollama connection failed: {response.status}")
            
            data = await response.json()
            models = [model['name'] for model in data.get('models', [])]
            
            if self.model not in models:
                logging.warning(f"Model {self.model} not found in Ollama. Available: {models}")
    
    async def _test_openai_connection(self) -> None:
        """Test OpenAI connection."""
        if not settings.OPENAI_API_KEY:
            raise LLMError("OpenAI API key not configured")
        
        headers = {
            "Authorization": f"Bearer {settings.OPENAI_API_KEY}",
            "Content-Type": "application/json"
        }
        
        url = "https://api.openai.com/v1/models"
        async with self.session.get(url, headers=headers) as response:
            if response.status != 200:
                raise LLMError(f"OpenAI connection failed: {response.status}")
    
    async def _test_huggingface_connection(self) -> None:
        """Test Hugging Face connection."""
        if not settings.HUGGINGFACE_API_KEY:
            raise LLMError("Hugging Face API key not configured")
        
        headers = {
            "Authorization": f"Bearer {settings.HUGGINGFACE_API_KEY}",
            "Content-Type": "application/json"
        }
        
        url = f"https://api-inference.huggingface.co/models/{settings.HUGGINGFACE_MODEL}"
        test_payload = {"inputs": "test"}
        
        async with self.session.post(url, headers=headers, json=test_payload) as response:
            if response.status not in [200, 503]:  # 503 is model loading
                raise LLMError(f"Hugging Face connection failed: {response.status}")
    
    async def generate_response(
        self,
        prompt: str,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        system_prompt: Optional[str] = None,
        stream: bool = False
    ) -> str:
        """
        Generate a response from the LLM.
        
        Args:
            prompt: Input prompt for the LLM
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            system_prompt: System prompt for context
            stream: Whether to stream the response
            
        Returns:
            Generated response text
        """
        try:
            # Rate limiting
            await self._check_rate_limit()
            
            # Use provided parameters or defaults
            max_tokens = max_tokens or self.max_tokens
            temperature = temperature or self.temperature
            
            # Generate response based on provider
            if self.provider == "ollama":
                return await self._generate_ollama_response(
                    prompt, max_tokens, temperature, system_prompt, stream
                )
            elif self.provider == "openai":
                return await self._generate_openai_response(
                    prompt, max_tokens, temperature, system_prompt, stream
                )
            elif self.provider == "huggingface":
                return await self._generate_huggingface_response(
                    prompt, max_tokens, temperature, system_prompt
                )
            else:
                raise LLMError(f"Unsupported provider: {self.provider}")
                
        except Exception as e:
            logging.error(f"LLM response generation failed: {e}")
            raise LLMError(f"Failed to generate response: {str(e)}")
    
    async def _generate_ollama_response(
        self,
        prompt: str,
        max_tokens: int,
        temperature: float,
        system_prompt: Optional[str],
        stream: bool
    ) -> str:
        """Generate response using Ollama."""
        url = f"{settings.OLLAMA_BASE_URL}/api/generate"
        
        payload = {
            "model": self.model,
            "prompt": prompt,
            "stream": stream,
            "options": {
                "temperature": temperature,
                "num_predict": max_tokens,
                "top_p": 0.9,
                "top_k": 40
            }
        }
        
        if system_prompt:
            payload["system"] = system_prompt
        
        if stream:
            return await self._handle_ollama_stream(url, payload)
        else:
            async with self.session.post(url, json=payload) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise LLMError(f"Ollama API error: {response.status} - {error_text}")
                
                data = await response.json()
                return data.get("response", "").strip()
    
    async def _handle_ollama_stream(self, url: str, payload: Dict[str, Any]) -> str:
        """Handle streaming response from Ollama."""
        full_response = ""
        
        async with self.session.post(url, json=payload) as response:
            if response.status != 200:
                error_text = await response.text()
                raise LLMError(f"Ollama streaming error: {response.status} - {error_text}")
            
            async for line in response.content:
                if line:
                    try:
                        data = json.loads(line.decode('utf-8'))
                        if 'response' in data:
                            full_response += data['response']
                        if data.get('done', False):
                            break
                    except json.JSONDecodeError:
                        continue
        
        return full_response.strip()
    
    async def _generate_openai_response(
        self,
        prompt: str,
        max_tokens: int,
        temperature: float,
        system_prompt: Optional[str],
        stream: bool
    ) -> str:
        """Generate response using OpenAI."""
        url = "https://api.openai.com/v1/chat/completions"
        
        headers = {
            "Authorization": f"Bearer {settings.OPENAI_API_KEY}",
            "Content-Type": "application/json"
        }
        
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        payload = {
            "model": settings.OPENAI_MODEL,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stream": stream
        }
        
        async with self.session.post(url, headers=headers, json=payload) as response:
            if response.status != 200:
                error_text = await response.text()
                raise LLMError(f"OpenAI API error: {response.status} - {error_text}")
            
            data = await response.json()
            
            if 'choices' in data and data['choices']:
                return data['choices'][0]['message']['content'].strip()
            else:
                raise LLMError("No response from OpenAI")
    
    async def _generate_huggingface_response(
        self,
        prompt: str,
        max_tokens: int,
        temperature: float,
        system_prompt: Optional[str]
    ) -> str:
        """Generate response using Hugging Face."""
        url = f"https://api-inference.huggingface.co/models/{settings.HUGGINGFACE_MODEL}"
        
        headers = {
            "Authorization": f"Bearer {settings.HUGGINGFACE_API_KEY}",
            "Content-Type": "application/json"
        }
        
        # Combine system prompt and user prompt
        full_prompt = prompt
        if system_prompt:
            full_prompt = f"{system_prompt}\n\nUser: {prompt}\nAssistant:"
        
        payload = {
            "inputs": full_prompt,
            "parameters": {
                "max_new_tokens": max_tokens,
                "temperature": temperature,
                "do_sample": True,
                "top_p": 0.9
            }
        }
        
        # Retry logic for model loading
        max_retries = 3
        for attempt in range(max_retries):
            async with self.session.post(url, headers=headers, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    if isinstance(data, list) and data:
                        generated_text = data[0].get('generated_text', '')
                        # Remove the input prompt from the response
                        if generated_text.startswith(full_prompt):
                            generated_text = generated_text[len(full_prompt):].strip()
                        return generated_text
                    else:
                        raise LLMError("Invalid response format from Hugging Face")
                elif response.status == 503:
                    # Model is loading, wait and retry
                    if attempt < max_retries - 1:
                        await asyncio.sleep(2 ** attempt)
                        continue
                    else:
                        raise LLMError("Model loading timeout")
                else:
                    error_text = await response.text()
                    raise LLMError(f"Hugging Face API error: {response.status} - {error_text}")
        
        raise LLMError("Failed to get response from Hugging Face")
    
    async def _check_rate_limit(self) -> None:
        """Check and enforce rate limiting."""
        current_time = time.time()
        
        # Simple rate limiting: max 60 requests per minute
        if current_time - self.last_request_time < 1.0:  # 1 second between requests
            if self.request_count >= 60:
                raise RateLimitError("Rate limit exceeded")
        else:
            self.request_count = 0
        
        self.request_count += 1
        self.last_request_time = current_time
    
    async def moderate_content(self, text: str) -> Dict[str, Any]:
        """
        Moderate content for safety and appropriateness.
        
        Args:
            text: Text to moderate
            
        Returns:
            Moderation results with flags and scores
        """
        try:
            # Basic content moderation (can be enhanced with external services)
            inappropriate_keywords = [
                'violence', 'harm', 'dangerous', 'illegal', 'unsafe'
            ]
            
            text_lower = text.lower()
            flags = []
            
            for keyword in inappropriate_keywords:
                if keyword in text_lower:
                    flags.append(keyword)
            
            return {
                'flagged': len(flags) > 0,
                'flags': flags,
                'confidence': 0.8 if flags else 0.1,
                'safe': len(flags) == 0
            }
            
        except Exception as e:
            logging.error(f"Content moderation failed: {e}")
            return {
                'flagged': False,
                'flags': [],
                'confidence': 0.0,
                'safe': True,
                'error': str(e)
            }
    
    async def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model."""
        return {
            'provider': self.provider,
            'model': self.model,
            'temperature': self.temperature,
            'max_tokens': self.max_tokens,
            'status': 'active',
            'last_used': datetime.utcnow().isoformat()
        }
    
    async def cleanup(self) -> None:
        """Clean up resources."""
        try:
            if self.session:
                await self.session.close()
            
            logging.info("LLM service cleanup completed")
            
        except Exception as e:
            logging.error(f"LLM service cleanup failed: {e}")


# Global LLM service instance
llm_service = LLMService()
