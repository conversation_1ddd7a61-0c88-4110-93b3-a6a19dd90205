"""
HomeCraft Intelligence - Authentication Service

This module handles user authentication, JWT token management,
password hashing, and session management for the application.
"""

import uuid
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from passlib.context import CryptContext
from jose import J<PERSON><PERSON>rror, jwt
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
import logging

from app.config import settings
from app.models.user import User, UserSession, UserStatus
from app.core.exceptions import AuthenticationError, AuthorizationError


class AuthService:
    """Service class for handling authentication operations."""
    
    def __init__(self):
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        self.secret_key = settings.SECRET_KEY
        self.algorithm = settings.ALGORITHM
        self.access_token_expire_minutes = settings.ACCESS_TOKEN_EXPIRE_MINUTES
        self.refresh_token_expire_days = settings.REFRESH_TOKEN_EXPIRE_DAYS
    
    def hash_password(self, password: str) -> str:
        """Hash a password using bcrypt."""
        return self.pwd_context.hash(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash."""
        return self.pwd_context.verify(plain_password, hashed_password)
    
    def create_access_token(self, data: Dict[str, Any]) -> str:
        """Create a JWT access token."""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        to_encode.update({"exp": expire, "type": "access"})
        
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def create_refresh_token(self, data: Dict[str, Any]) -> str:
        """Create a JWT refresh token."""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=self.refresh_token_expire_days)
        to_encode.update({"exp": expire, "type": "refresh"})
        
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def verify_token(self, token: str, token_type: str = "access") -> Dict[str, Any]:
        """Verify and decode a JWT token."""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # Check token type
            if payload.get("type") != token_type:
                raise AuthenticationError("Invalid token type")
            
            # Check expiration
            exp = payload.get("exp")
            if exp is None or datetime.utcnow() > datetime.fromtimestamp(exp):
                raise AuthenticationError("Token has expired")
            
            return payload
            
        except JWTError as e:
            logging.warning(f"JWT verification failed: {e}")
            raise AuthenticationError("Invalid token")
    
    async def authenticate_user(self, db: AsyncSession, email: str, password: str) -> Optional[User]:
        """Authenticate a user with email and password."""
        # Get user by email
        stmt = select(User).where(User.email == email, User.deleted_at.is_(None))
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if not user:
            return None
        
        # Verify password
        if not self.verify_password(password, user.hashed_password):
            return None
        
        # Check if user is active
        if not user.is_active or user.status == UserStatus.SUSPENDED:
            raise AuthenticationError("Account is inactive or suspended")
        
        return user
    
    async def create_user_session(
        self, 
        db: AsyncSession, 
        user: User, 
        device_info: Dict[str, Any] = None,
        ip_address: str = None,
        user_agent: str = None
    ) -> Dict[str, Any]:
        """Create a new user session with tokens."""
        
        # Create token payload
        token_data = {
            "sub": str(user.id),
            "email": user.email,
            "skill_level": user.skill_level.value,
            "is_premium": user.is_premium
        }
        
        # Generate tokens
        access_token = self.create_access_token(token_data)
        refresh_token = self.create_refresh_token({"sub": str(user.id)})
        
        # Create session record
        session = UserSession(
            user_id=user.id,
            session_token=access_token,
            refresh_token=refresh_token,
            device_info=device_info or {},
            ip_address=ip_address,
            user_agent=user_agent,
            expires_at=datetime.utcnow() + timedelta(days=self.refresh_token_expire_days)
        )
        
        db.add(session)
        await db.commit()
        await db.refresh(session)
        
        # Update user's last active time
        user.update_last_active()
        await db.commit()
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "expires_in": self.access_token_expire_minutes * 60,
            "user": {
                "id": str(user.id),
                "email": user.email,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "skill_level": user.skill_level.value,
                "is_premium": user.is_premium
            }
        }
    
    async def refresh_access_token(self, db: AsyncSession, refresh_token: str) -> Dict[str, Any]:
        """Refresh an access token using a refresh token."""
        
        # Verify refresh token
        try:
            payload = self.verify_token(refresh_token, "refresh")
            user_id = payload.get("sub")
            
            if not user_id:
                raise AuthenticationError("Invalid token payload")
            
        except AuthenticationError:
            raise AuthenticationError("Invalid refresh token")
        
        # Get user session
        stmt = select(UserSession).where(
            UserSession.refresh_token == refresh_token,
            UserSession.is_active == True
        )
        result = await db.execute(stmt)
        session = result.scalar_one_or_none()
        
        if not session or session.is_expired:
            raise AuthenticationError("Session expired or invalid")
        
        # Get user
        stmt = select(User).where(User.id == user_id, User.deleted_at.is_(None))
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if not user or not user.is_active:
            raise AuthenticationError("User not found or inactive")
        
        # Create new access token
        token_data = {
            "sub": str(user.id),
            "email": user.email,
            "skill_level": user.skill_level.value,
            "is_premium": user.is_premium
        }
        
        new_access_token = self.create_access_token(token_data)
        
        # Update session
        session.session_token = new_access_token
        session.update_last_used()
        await db.commit()
        
        # Update user's last active time
        user.update_last_active()
        await db.commit()
        
        return {
            "access_token": new_access_token,
            "token_type": "bearer",
            "expires_in": self.access_token_expire_minutes * 60
        }
    
    async def revoke_session(self, db: AsyncSession, refresh_token: str) -> bool:
        """Revoke a user session."""
        stmt = select(UserSession).where(UserSession.refresh_token == refresh_token)
        result = await db.execute(stmt)
        session = result.scalar_one_or_none()
        
        if session:
            session.revoke()
            await db.commit()
            return True
        
        return False
    
    async def revoke_all_sessions(self, db: AsyncSession, user_id: uuid.UUID) -> int:
        """Revoke all sessions for a user."""
        stmt = (
            update(UserSession)
            .where(UserSession.user_id == user_id, UserSession.is_active == True)
            .values(is_active=False, revoked_at=datetime.utcnow())
        )
        result = await db.execute(stmt)
        await db.commit()
        return result.rowcount
    
    async def get_current_user(self, db: AsyncSession, token: str) -> User:
        """Get current user from access token."""
        try:
            payload = self.verify_token(token, "access")
            user_id = payload.get("sub")
            
            if not user_id:
                raise AuthenticationError("Invalid token payload")
            
            # Get user
            stmt = select(User).where(User.id == user_id, User.deleted_at.is_(None))
            result = await db.execute(stmt)
            user = result.scalar_one_or_none()
            
            if not user:
                raise AuthenticationError("User not found")
            
            if not user.is_active:
                raise AuthenticationError("User account is inactive")
            
            return user
            
        except AuthenticationError:
            raise
        except Exception as e:
            logging.error(f"Error getting current user: {e}")
            raise AuthenticationError("Authentication failed")
    
    async def check_permission(self, user: User, required_permission: str) -> bool:
        """Check if user has required permission."""
        # Basic permission system - can be extended
        permissions = {
            "create_project": True,  # All users can create projects
            "admin_access": user.is_premium,  # Only premium users get admin features
            "advanced_features": user.skill_level.value in ["advanced", "professional"]
        }
        
        return permissions.get(required_permission, False)
    
    def generate_password_reset_token(self, user_id: str) -> str:
        """Generate a password reset token."""
        data = {
            "sub": user_id,
            "type": "password_reset",
            "exp": datetime.utcnow() + timedelta(hours=1)  # 1 hour expiry
        }
        return jwt.encode(data, self.secret_key, algorithm=self.algorithm)
    
    def verify_password_reset_token(self, token: str) -> Optional[str]:
        """Verify password reset token and return user ID."""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            if payload.get("type") != "password_reset":
                return None
            
            return payload.get("sub")
            
        except JWTError:
            return None


# Global auth service instance
auth_service = AuthService()
