# HomeCraft Intelligence - Backend Dependencies
# Production-ready Python packages for FastAPI application

# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0

# Database
sqlalchemy[asyncio]==2.0.23
asyncpg==0.29.0
alembic==1.12.1
psycopg2-binary==2.9.9

# Redis & Caching
redis[hiredis]==5.0.1
aioredis==2.0.1

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
bcrypt==4.1.2

# HTTP Client
httpx==0.25.2
aiohttp==3.9.1

# AI/ML Dependencies
sentence-transformers==2.2.2
chromadb==0.4.18
numpy==1.24.4
torch==2.1.1
transformers==4.35.2
huggingface-hub==0.19.4

# Data Processing
pandas==2.1.4
pydantic==2.5.0
pydantic-settings==2.1.0

# Rate Limiting
slowapi==0.1.9
limits==3.6.0

# Validation & Serialization
email-validator==2.1.0
python-dateutil==2.8.2

# File Handling
python-magic==0.4.27
pillow==10.1.0

# Monitoring & Logging
prometheus-client==0.19.0
structlog==23.2.0

# Environment & Configuration
python-dotenv==1.0.0
pyyaml==6.0.1

# Testing (Development)
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2
faker==20.1.0

# Code Quality (Development)
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Documentation
mkdocs==1.5.3
mkdocs-material==9.4.8

# Utilities
click==8.1.7
rich==13.7.0
typer==0.9.0

# Background Tasks
celery[redis]==5.3.4
flower==2.0.1

# Email (Optional)
fastapi-mail==1.4.1

# Websockets
websockets==12.0

# CORS & Middleware
python-cors==1.7.0

# JSON Web Tokens
pyjwt==2.8.0

# HTTP Status Codes
http-status==0.2.1

# UUID Utilities
shortuuid==1.0.11

# Time & Date
arrow==1.3.0

# Cryptography
cryptography==41.0.8

# Development Tools
watchdog==3.0.0
pre-commit==3.6.0

# Production WSGI Server
gevent==23.9.1

# Memory Profiling
memory-profiler==0.61.0

# API Documentation
fastapi-users==12.1.2

# File Upload
aiofiles==23.2.1

# Image Processing
opencv-python-headless==********

# Natural Language Processing
spacy==3.7.2
nltk==3.8.1

# Mathematical Operations
scipy==1.11.4

# Configuration Management
dynaconf==3.2.4

# Async Database Toolkit
databases[postgresql]==0.8.0

# Migration Tools
yoyo-migrations==8.2.0

# API Rate Limiting
flask-limiter==3.5.0

# Caching
cachetools==5.3.2

# Serialization
orjson==3.9.10

# Timezone Handling
pytz==2023.3

# Regular Expressions
regex==2023.10.3

# URL Parsing
yarl==1.9.4

# Async Utilities
asyncio-mqtt==0.16.1

# Health Checks
healthcheck==1.3.3

# Metrics Collection
statsd==4.0.1

# Error Tracking
sentry-sdk[fastapi]==1.38.0

# Performance Monitoring
py-spy==0.3.14

# Load Testing
locust==2.17.0
