# HomeCraft Intelligence - Frontend Environment Configuration
# Copy this file to .env.local and update the values for your environment

# Application Configuration
NEXT_PUBLIC_APP_NAME="HomeCraft Intelligence"
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_APP_URL=https://homecraft-intelligence.com

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_API_TIMEOUT=30000

# Authentication Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-change-in-production

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_CHAT=true
NEXT_PUBLIC_ENABLE_COMMUNITY=true
NEXT_PUBLIC_ENABLE_PREMIUM_FEATURES=true

# Analytics Configuration
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
NEXT_PUBLIC_GTM_ID=GTM-XXXXXXX

# Error Tracking
NEXT_PUBLIC_SENTRY_DSN=your-sentry-dsn-here

# Map Services (Optional)
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your-google-maps-api-key-here

# Social Media Links
NEXT_PUBLIC_TWITTER_URL=https://twitter.com/homecraftai
NEXT_PUBLIC_FACEBOOK_URL=https://facebook.com/homecraftai
NEXT_PUBLIC_INSTAGRAM_URL=https://instagram.com/homecraftai

# Support Configuration
NEXT_PUBLIC_SUPPORT_EMAIL=<EMAIL>
NEXT_PUBLIC_SUPPORT_PHONE=******-123-4567

# Development Configuration
NODE_ENV=development
NEXT_PUBLIC_DEBUG=true

# Build Configuration
ANALYZE=false
BUNDLE_ANALYZE=false
