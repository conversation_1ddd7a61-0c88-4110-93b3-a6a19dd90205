"""
HomeCraft Intelligence - Authentication API Routes

This module defines the authentication endpoints for user registration,
login, token refresh, and session management.
"""

from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
import logging

from app.database import get_db
from app.models.user import User, SkillLevel, UserStatus
from app.services.auth_service import auth_service
from app.schemas.user import (
    UserCreate, UserLogin, UserResponse, TokenResponse,
    PasswordReset, PasswordResetConfirm
)
from app.core.exceptions import AuthenticationError, ValidationError
from app.utils.validation import validate_email, validate_password


router = APIRouter()
security = HTTPBearer()


@router.post("/register", response_model=TokenResponse, status_code=status.HTTP_201_CREATED)
async def register_user(
    user_data: UserCreate,
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    Register a new user account.
    
    Creates a new user with the provided information and returns
    authentication tokens for immediate login.
    """
    try:
        # Validate input data
        if not validate_email(user_data.email):
            raise ValidationError("Invalid email format")
        
        if not validate_password(user_data.password):
            raise ValidationError(
                "Password must be at least 8 characters long and contain "
                "uppercase, lowercase, number, and special character"
            )
        
        # Check if user already exists
        stmt = select(User).where(User.email == user_data.email)
        result = await db.execute(stmt)
        existing_user = result.scalar_one_or_none()
        
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        
        # Create new user
        hashed_password = auth_service.hash_password(user_data.password)
        
        new_user = User(
            email=user_data.email,
            hashed_password=hashed_password,
            first_name=user_data.first_name,
            last_name=user_data.last_name,
            skill_level=user_data.skill_level or SkillLevel.BEGINNER,
            location=user_data.location,
            status=UserStatus.ACTIVE,  # Auto-activate for now
            is_verified=True  # Auto-verify for now
        )
        
        db.add(new_user)
        await db.commit()
        await db.refresh(new_user)
        
        # Create session and tokens
        device_info = {
            "user_agent": request.headers.get("user-agent", ""),
            "platform": "web"
        }
        
        session_data = await auth_service.create_user_session(
            db=db,
            user=new_user,
            device_info=device_info,
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent")
        )
        
        logging.info(f"New user registered: {new_user.email}")
        
        return TokenResponse(**session_data)
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logging.error(f"User registration failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


@router.post("/login", response_model=TokenResponse)
async def login_user(
    login_data: UserLogin,
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    Authenticate user and return access tokens.
    
    Validates user credentials and creates a new session
    with access and refresh tokens.
    """
    try:
        # Authenticate user
        user = await auth_service.authenticate_user(
            db=db,
            email=login_data.email,
            password=login_data.password
        )
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )
        
        # Create session and tokens
        device_info = {
            "user_agent": request.headers.get("user-agent", ""),
            "platform": "web"
        }
        
        session_data = await auth_service.create_user_session(
            db=db,
            user=user,
            device_info=device_info,
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent")
        )
        
        logging.info(f"User logged in: {user.email}")
        
        return TokenResponse(**session_data)
        
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except Exception as e:
        logging.error(f"User login failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.post("/refresh", response_model=Dict[str, Any])
async def refresh_token(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
):
    """
    Refresh access token using refresh token.
    
    Takes a refresh token and returns a new access token
    if the refresh token is valid and not expired.
    """
    try:
        refresh_token = credentials.credentials
        
        token_data = await auth_service.refresh_access_token(
            db=db,
            refresh_token=refresh_token
        )
        
        return token_data
        
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except Exception as e:
        logging.error(f"Token refresh failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )


@router.post("/logout")
async def logout_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
):
    """
    Logout user and revoke session.
    
    Revokes the current session and invalidates the refresh token.
    """
    try:
        refresh_token = credentials.credentials
        
        success = await auth_service.revoke_session(
            db=db,
            refresh_token=refresh_token
        )
        
        if success:
            return {"message": "Successfully logged out"}
        else:
            return {"message": "Session not found or already expired"}
        
    except Exception as e:
        logging.error(f"Logout failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed"
        )


@router.post("/logout-all")
async def logout_all_sessions(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
):
    """
    Logout from all sessions.
    
    Revokes all active sessions for the current user.
    """
    try:
        # Get current user from token
        access_token = credentials.credentials
        user = await auth_service.get_current_user(db, access_token)
        
        # Revoke all sessions
        revoked_count = await auth_service.revoke_all_sessions(
            db=db,
            user_id=user.id
        )
        
        return {
            "message": f"Successfully logged out from {revoked_count} sessions"
        }
        
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except Exception as e:
        logging.error(f"Logout all failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout all failed"
        )


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
):
    """
    Get current user information.
    
    Returns the profile information of the currently authenticated user.
    """
    try:
        access_token = credentials.credentials
        user = await auth_service.get_current_user(db, access_token)
        
        return UserResponse(
            id=str(user.id),
            email=user.email,
            first_name=user.first_name,
            last_name=user.last_name,
            username=user.username,
            skill_level=user.skill_level,
            location=user.location,
            is_premium=user.is_premium,
            total_projects=user.total_projects,
            completed_projects=user.completed_projects,
            completion_rate=user.completion_rate,
            created_at=user.created_at,
            last_active_at=user.last_active_at
        )
        
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except Exception as e:
        logging.error(f"Get current user failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user information"
        )


@router.post("/password-reset")
async def request_password_reset(
    reset_data: PasswordReset,
    db: AsyncSession = Depends(get_db)
):
    """
    Request password reset.
    
    Sends a password reset email to the user if the email exists.
    Always returns success to prevent email enumeration.
    """
    try:
        # Check if user exists
        stmt = select(User).where(User.email == reset_data.email)
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if user:
            # Generate reset token
            reset_token = auth_service.generate_password_reset_token(str(user.id))
            
            # TODO: Send email with reset token
            # For now, just log it (in production, send email)
            logging.info(f"Password reset requested for {user.email}, token: {reset_token}")
        
        # Always return success to prevent email enumeration
        return {"message": "If the email exists, a password reset link has been sent"}
        
    except Exception as e:
        logging.error(f"Password reset request failed: {e}")
        return {"message": "If the email exists, a password reset link has been sent"}


@router.post("/password-reset/confirm")
async def confirm_password_reset(
    reset_data: PasswordResetConfirm,
    db: AsyncSession = Depends(get_db)
):
    """
    Confirm password reset with token.
    
    Resets the user's password using the provided reset token.
    """
    try:
        # Verify reset token
        user_id = auth_service.verify_password_reset_token(reset_data.token)
        
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired reset token"
            )
        
        # Validate new password
        if not validate_password(reset_data.new_password):
            raise ValidationError(
                "Password must be at least 8 characters long and contain "
                "uppercase, lowercase, number, and special character"
            )
        
        # Get user and update password
        stmt = select(User).where(User.id == user_id)
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User not found"
            )
        
        # Update password
        user.hashed_password = auth_service.hash_password(reset_data.new_password)
        await db.commit()
        
        # Revoke all existing sessions
        await auth_service.revoke_all_sessions(db, user.id)
        
        logging.info(f"Password reset completed for user: {user.email}")
        
        return {"message": "Password reset successfully"}
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logging.error(f"Password reset confirmation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password reset failed"
        )
