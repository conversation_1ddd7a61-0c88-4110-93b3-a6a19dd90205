# HomeCraft Intelligence API Documentation

## Overview

The HomeCraft Intelligence API provides comprehensive endpoints for managing DIY home improvement projects, RAG-powered assistance, safety compliance, and community features.

**Base URL**: `https://api.homecraft-intelligence.com/api/v1`  
**Authentication**: JWT Bearer Token  
**Content-Type**: `application/json`

## Authentication

### Register User
```http
POST /auth/register
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "skill_level": "beginner",
  "location": "California, USA"
}
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "first_name": "<PERSON>",
    "last_name": "<PERSON><PERSON>",
    "skill_level": "beginner"
  }
}
```

### Login
```http
POST /auth/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

### Refresh Token
```http
POST /auth/refresh
```

**Headers:**
```
Authorization: Bearer <refresh_token>
```

## Projects API

### List Projects
```http
GET /projects/
```

**Query Parameters:**
- `skip` (int): Number of records to skip (default: 0)
- `limit` (int): Number of records to return (default: 20)
- `status` (string): Filter by project status
- `skill_level` (string): Filter by required skill level

**Response:**
```json
{
  "projects": [
    {
      "id": "uuid",
      "title": "Kitchen Cabinet Installation",
      "description": "Installing new kitchen cabinets",
      "status": "in_progress",
      "skill_level": "intermediate",
      "estimated_duration": "2-3 days",
      "safety_score": 85,
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-16T14:20:00Z"
    }
  ],
  "total": 1,
  "skip": 0,
  "limit": 20
}
```

### Create Project
```http
POST /projects/
```

**Request Body:**
```json
{
  "title": "Bathroom Tile Installation",
  "description": "Installing ceramic tiles in master bathroom",
  "project_type": "flooring",
  "room_type": "bathroom",
  "estimated_budget": 1500.00,
  "target_completion": "2024-02-01",
  "materials_needed": [
    {
      "name": "Ceramic tiles",
      "quantity": 50,
      "unit": "sq_ft"
    }
  ]
}
```

### Get Project Details
```http
GET /projects/{project_id}
```

**Response:**
```json
{
  "id": "uuid",
  "title": "Bathroom Tile Installation",
  "description": "Installing ceramic tiles in master bathroom",
  "status": "planning",
  "progress_percentage": 15,
  "steps": [
    {
      "id": "uuid",
      "title": "Prepare surface",
      "description": "Clean and level the floor",
      "status": "completed",
      "safety_notes": ["Wear safety glasses", "Ensure proper ventilation"],
      "estimated_time": "2 hours"
    }
  ],
  "materials": [
    {
      "name": "Ceramic tiles",
      "quantity_needed": 50,
      "quantity_purchased": 55,
      "unit": "sq_ft",
      "cost_per_unit": 3.50,
      "total_cost": 192.50
    }
  ],
  "safety_checklist": [
    {
      "item": "Safety glasses",
      "required": true,
      "completed": true
    }
  ]
}
```

## RAG Assistant API

### Query Assistant
```http
POST /rag/query
```

**Request Body:**
```json
{
  "query": "How do I safely install a ceiling fan?",
  "context": {
    "project_id": "uuid",
    "skill_level": "beginner",
    "location": "California, USA",
    "room_details": {
      "ceiling_height": 8,
      "room_type": "bedroom",
      "electrical_access": true
    }
  },
  "include_safety": true,
  "include_materials": true
}
```

**Response:**
```json
{
  "response": "To safely install a ceiling fan in an 8-foot ceiling bedroom...",
  "sources": [
    {
      "title": "Ceiling Fan Installation Guide",
      "url": "internal://guides/ceiling-fan-installation",
      "relevance_score": 0.95
    }
  ],
  "safety_warnings": [
    "Turn off electricity at the circuit breaker",
    "Use a sturdy ladder with someone spotting you"
  ],
  "materials_suggested": [
    {
      "name": "Ceiling fan mounting bracket",
      "quantity": 1,
      "estimated_cost": 25.00
    }
  ],
  "follow_up_questions": [
    "Do you need help selecting the right ceiling fan size?",
    "Would you like a step-by-step installation checklist?"
  ]
}
```

### Get Query Suggestions
```http
GET /rag/suggestions
```

**Query Parameters:**
- `project_type` (string): Type of project for relevant suggestions
- `skill_level` (string): User's skill level

**Response:**
```json
{
  "suggestions": [
    "How do I prepare walls for painting?",
    "What tools do I need for basic plumbing repairs?",
    "How to safely work with electrical wiring?"
  ]
}
```

## Materials API

### Calculate Materials
```http
POST /materials/calculate
```

**Request Body:**
```json
{
  "project_type": "flooring",
  "room_dimensions": {
    "length": 12,
    "width": 10,
    "unit": "feet"
  },
  "material_type": "hardwood",
  "waste_factor": 0.1
}
```

**Response:**
```json
{
  "total_area": 120,
  "material_needed": 132,
  "waste_allowance": 12,
  "materials": [
    {
      "name": "Hardwood flooring",
      "quantity": 132,
      "unit": "sq_ft",
      "estimated_cost_per_unit": 8.50,
      "total_cost": 1122.00
    },
    {
      "name": "Underlayment",
      "quantity": 132,
      "unit": "sq_ft",
      "estimated_cost_per_unit": 0.75,
      "total_cost": 99.00
    }
  ],
  "tools_needed": [
    "Miter saw",
    "Nail gun",
    "Measuring tape"
  ],
  "total_estimated_cost": 1221.00
}
```

## Safety API

### Safety Compliance Check
```http
POST /safety/check
```

**Request Body:**
```json
{
  "project_description": "Installing electrical outlet in bathroom",
  "location": "California, USA",
  "room_type": "bathroom",
  "electrical_work": true
}
```

**Response:**
```json
{
  "compliance_score": 75,
  "warnings": [
    "GFCI outlet required in bathroom locations",
    "Electrical permit may be required"
  ],
  "requirements": [
    {
      "code": "NEC 210.8(A)(1)",
      "description": "GFCI protection required for bathroom outlets",
      "compliance_status": "needs_attention"
    }
  ],
  "recommendations": [
    "Consult with licensed electrician",
    "Check local permit requirements"
  ]
}
```

## Error Responses

All endpoints may return the following error responses:

### 400 Bad Request
```json
{
  "detail": "Validation error",
  "errors": [
    {
      "field": "email",
      "message": "Invalid email format"
    }
  ]
}
```

### 401 Unauthorized
```json
{
  "detail": "Invalid or expired token"
}
```

### 403 Forbidden
```json
{
  "detail": "Insufficient permissions"
}
```

### 404 Not Found
```json
{
  "detail": "Resource not found"
}
```

### 429 Too Many Requests
```json
{
  "detail": "Rate limit exceeded",
  "retry_after": 60
}
```

### 500 Internal Server Error
```json
{
  "detail": "Internal server error",
  "error_id": "uuid"
}
```

## Rate Limiting

- **Authentication endpoints**: 5 requests per minute
- **RAG queries**: 20 requests per minute
- **General API**: 100 requests per minute
- **File uploads**: 10 requests per minute

## Webhooks

### Project Status Updates
```http
POST /webhooks/project-status
```

**Payload:**
```json
{
  "event": "project.status_changed",
  "project_id": "uuid",
  "old_status": "in_progress",
  "new_status": "completed",
  "timestamp": "2024-01-16T14:20:00Z"
}
```

## SDKs and Libraries

- **Python**: `pip install homecraft-intelligence-sdk`
- **JavaScript**: `npm install @homecraft/intelligence-sdk`
- **cURL examples**: Available in each endpoint documentation

## Support

- **API Status**: https://status.homecraft-intelligence.com
- **Support Email**: <EMAIL>
- **Documentation**: https://docs.homecraft-intelligence.com
