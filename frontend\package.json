{"name": "homecraft-intelligence-frontend", "version": "1.0.0", "description": "HomeCraft Intelligence - AI-Powered DIY Home Improvement Assistant Frontend", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "analyze": "cross-env ANALYZE=true next build", "clean": "rimraf .next out dist"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.2.0", "@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "@tailwindcss/forms": "^0.5.0", "@tailwindcss/typography": "^0.5.0", "@headlessui/react": "^1.7.0", "@heroicons/react": "^2.0.0", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0", "lucide-react": "^0.290.0", "zustand": "^4.4.0", "axios": "^1.5.0", "react-hook-form": "^7.47.0", "@hookform/resolvers": "^3.3.0", "zod": "^3.22.0", "next-auth": "^4.24.0", "react-query": "^3.39.0", "framer-motion": "^10.16.0", "react-hot-toast": "^2.4.0", "react-markdown": "^9.0.0", "remark-gfm": "^4.0.0", "rehype-highlight": "^7.0.0", "date-fns": "^2.30.0", "react-intersection-observer": "^9.5.0", "react-dropzone": "^14.2.0", "recharts": "^2.8.0", "react-virtualized-auto-sizer": "^1.0.0", "react-window": "^1.8.0"}, "devDependencies": {"eslint": "^8.51.0", "eslint-config-next": "^14.0.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.0", "@testing-library/user-event": "^14.5.0", "playwright": "^1.39.0", "@playwright/test": "^1.39.0", "cross-env": "^7.0.0", "rimraf": "^5.0.0", "@next/bundle-analyzer": "^14.0.0", "husky": "^8.0.0", "lint-staged": "^15.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run type-check && npm run test"}}, "keywords": ["diy", "home-improvement", "ai-assistant", "rag", "nextjs", "react", "typescript", "tailwindcss"], "author": "HomeCraft Intelligence Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/homecraft-intelligence.git"}, "bugs": {"url": "https://github.com/HectorTa1989/homecraft-intelligence/issues"}, "homepage": "https://homecraft-intelligence.com"}