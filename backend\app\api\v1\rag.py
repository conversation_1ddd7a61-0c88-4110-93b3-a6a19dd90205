"""
HomeCraft Intelligence - RAG API Routes

This module defines the RAG (Retrieval-Augmented Generation) endpoints
for the AI assistant functionality, including query processing, suggestions,
and feedback collection.
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
import logging
from datetime import datetime

from app.database import get_db
from app.services.auth_service import auth_service
from app.services.rag_service import rag_service
from app.schemas.rag import (
    RAGQuery, RAGResponse, RAGSuggestion, RAGFeedback,
    QueryContext, SafetyWarning, MaterialSuggestion
)
from app.core.exceptions import RAGError, AuthenticationError
from app.models.user import User


router = APIRouter()
security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """Dependency to get current authenticated user."""
    try:
        access_token = credentials.credentials
        return await auth_service.get_current_user(db, access_token)
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )


@router.post("/query", response_model=RAGResponse)
async def process_rag_query(
    query_data: RAGQuery,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Process a RAG query and return AI-generated response.
    
    This endpoint handles user queries about DIY projects, safety guidelines,
    and home improvement advice using the RAG pipeline.
    """
    try:
        # Prepare context with user information
        context = query_data.context.dict() if query_data.context else {}
        context.update({
            "user_id": str(current_user.id),
            "skill_level": current_user.skill_level.value,
            "location": current_user.location,
            "is_premium": current_user.is_premium
        })
        
        # Process query through RAG pipeline
        result = await rag_service.process_query(
            query=query_data.query,
            context=context,
            user_id=str(current_user.id),
            include_safety=query_data.include_safety,
            include_materials=query_data.include_materials
        )
        
        # Update user query count in background
        background_tasks.add_task(update_user_stats, current_user.id, db)
        
        # Convert result to response model
        response = RAGResponse(
            response=result["response"],
            confidence_score=result["confidence_score"],
            sources=result["sources"],
            safety_warnings=[
                SafetyWarning(
                    message=warning,
                    severity="high" if any(word in warning.lower() 
                                         for word in ["danger", "hazard", "risk"]) else "medium"
                )
                for warning in result["safety_warnings"]
            ],
            materials_suggested=[
                MaterialSuggestion(**material) 
                for material in result["materials_suggested"]
            ],
            follow_up_questions=result["follow_up_questions"],
            processing_time=result["processing_time"],
            metadata=result["metadata"]
        )
        
        logging.info(f"RAG query processed for user {current_user.id}: {query_data.query[:100]}...")
        
        return response
        
    except RAGError as e:
        logging.error(f"RAG processing error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Query processing failed: {str(e)}"
        )
    except Exception as e:
        logging.error(f"Unexpected error in RAG query: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while processing your query"
        )


@router.get("/suggestions", response_model=List[RAGSuggestion])
async def get_query_suggestions(
    project_type: Optional[str] = None,
    skill_level: Optional[str] = None,
    limit: int = 10,
    current_user: User = Depends(get_current_user)
):
    """
    Get suggested queries based on user context and popular questions.
    
    Returns a list of suggested questions that users commonly ask,
    filtered by project type and skill level.
    """
    try:
        # Use user's skill level if not provided
        if not skill_level:
            skill_level = current_user.skill_level.value
        
        # Predefined suggestions based on skill level and project type
        suggestions_db = {
            "beginner": {
                "general": [
                    "What basic tools do I need for home improvement projects?",
                    "How do I safely use power tools for the first time?",
                    "What are the most common DIY mistakes to avoid?",
                    "How do I know when to call a professional?",
                    "What safety equipment should I always have?"
                ],
                "painting": [
                    "How do I prepare walls for painting?",
                    "What type of paint should I use for different rooms?",
                    "How do I paint a room without making a mess?",
                    "What's the best way to paint trim and baseboards?"
                ],
                "plumbing": [
                    "How do I fix a leaky faucet?",
                    "What should I do if my toilet is clogged?",
                    "How do I shut off water in an emergency?",
                    "When should I call a plumber instead of DIY?"
                ]
            },
            "intermediate": {
                "general": [
                    "How do I plan a multi-room renovation project?",
                    "What permits do I need for home improvements?",
                    "How do I work safely with electrical systems?",
                    "What's the best way to manage project timelines?"
                ],
                "electrical": [
                    "How do I install a ceiling fan safely?",
                    "What's involved in adding new electrical outlets?",
                    "How do I upgrade an electrical panel?",
                    "What electrical work requires a professional?"
                ]
            },
            "advanced": {
                "general": [
                    "How do I design a structural renovation?",
                    "What are the latest building codes I should know?",
                    "How do I manage subcontractors effectively?",
                    "What advanced tools are worth the investment?"
                ]
            }
        }
        
        # Get suggestions based on parameters
        skill_suggestions = suggestions_db.get(skill_level, suggestions_db["beginner"])
        
        if project_type and project_type in skill_suggestions:
            questions = skill_suggestions[project_type]
        else:
            questions = skill_suggestions.get("general", skill_suggestions["general"])
        
        # Convert to response format
        suggestions = [
            RAGSuggestion(
                query=question,
                category=project_type or "general",
                skill_level=skill_level,
                popularity_score=0.8 - (i * 0.1)  # Decreasing popularity
            )
            for i, question in enumerate(questions[:limit])
        ]
        
        return suggestions
        
    except Exception as e:
        logging.error(f"Error getting query suggestions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get query suggestions"
        )


@router.post("/feedback")
async def submit_rag_feedback(
    feedback_data: RAGFeedback,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Submit feedback on a RAG response.
    
    Collects user feedback to improve the quality of AI responses
    and identify areas for knowledge base enhancement.
    """
    try:
        # TODO: Store feedback in database for analysis
        # For now, just log the feedback
        
        feedback_log = {
            "user_id": str(current_user.id),
            "query": feedback_data.query,
            "response_id": feedback_data.response_id,
            "rating": feedback_data.rating,
            "feedback_text": feedback_data.feedback_text,
            "categories": feedback_data.categories,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        logging.info(f"RAG feedback received: {feedback_log}")
        
        # Analyze feedback for immediate improvements
        if feedback_data.rating <= 2:
            logging.warning(f"Low rating feedback received: {feedback_data.feedback_text}")
        
        return {
            "message": "Thank you for your feedback! It helps us improve our responses.",
            "feedback_id": f"fb_{datetime.utcnow().timestamp()}"
        }
        
    except Exception as e:
        logging.error(f"Error submitting RAG feedback: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to submit feedback"
        )


@router.get("/history")
async def get_query_history(
    limit: int = 20,
    offset: int = 0,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get user's query history.
    
    Returns a paginated list of the user's previous queries and responses
    for reference and continuation of conversations.
    """
    try:
        # TODO: Implement query history retrieval from database
        # For now, return empty list
        
        return {
            "queries": [],
            "total": 0,
            "limit": limit,
            "offset": offset,
            "message": "Query history feature coming soon!"
        }
        
    except Exception as e:
        logging.error(f"Error getting query history: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get query history"
        )


@router.get("/stats")
async def get_rag_stats(
    current_user: User = Depends(get_current_user)
):
    """
    Get RAG usage statistics for the current user.
    
    Returns statistics about the user's interaction with the AI assistant,
    including query count, favorite topics, and usage patterns.
    """
    try:
        # Basic stats from user model
        stats = {
            "total_queries": current_user.total_queries,
            "queries_this_month": 0,  # TODO: Calculate from database
            "favorite_topics": [],  # TODO: Analyze from query history
            "average_rating": 0.0,  # TODO: Calculate from feedback
            "most_active_day": "Monday",  # TODO: Calculate from usage patterns
            "knowledge_areas": [
                {"topic": "Safety", "queries": 0},
                {"topic": "Tools", "queries": 0},
                {"topic": "Materials", "queries": 0},
                {"topic": "Techniques", "queries": 0}
            ]
        }
        
        return stats
        
    except Exception as e:
        logging.error(f"Error getting RAG stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get usage statistics"
        )


async def update_user_stats(user_id: str, db: AsyncSession):
    """Background task to update user statistics."""
    try:
        from sqlalchemy import update
        from app.models.user import User
        
        # Increment query count
        stmt = (
            update(User)
            .where(User.id == user_id)
            .values(
                total_queries=User.total_queries + 1,
                last_active_at=datetime.utcnow()
            )
        )
        
        await db.execute(stmt)
        await db.commit()
        
    except Exception as e:
        logging.error(f"Failed to update user stats: {e}")


@router.get("/health")
async def rag_health_check():
    """Health check endpoint for RAG service."""
    try:
        # TODO: Add actual health checks for RAG components
        return {
            "status": "healthy",
            "components": {
                "embedding_service": "healthy",
                "llm_service": "healthy",
                "vector_database": "healthy"
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logging.error(f"RAG health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }
