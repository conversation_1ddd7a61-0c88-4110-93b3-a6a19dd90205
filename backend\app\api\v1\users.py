"""
HomeCraft Intelligence - Users API Routes

This module defines the API endpoints for user management,
profile operations, and user statistics.

Author: HomeCraft Intelligence Team
License: MIT
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from pydantic import BaseModel, Field, EmailStr
import logging
from datetime import datetime

from app.database import get_db
from app.services.auth_service import auth_service
from app.models.user import User, SkillLevel
from app.models.project import Project, ProjectStatus
from app.core.exceptions import NotFoundError, ValidationError, AuthorizationError
from app.middleware.rate_limit import rate_limit


router = APIRouter()
security = HTTPBearer()


# Schemas
class UserProfileUpdate(BaseModel):
    """Schema for updating user profile."""
    first_name: Optional[str] = Field(None, min_length=1, max_length=50)
    last_name: Optional[str] = Field(None, min_length=1, max_length=50)
    username: Optional[str] = Field(None, min_length=3, max_length=30)
    location: Optional[str] = Field(None, max_length=200)
    skill_level: Optional[SkillLevel] = None
    bio: Optional[str] = Field(None, max_length=500)
    website: Optional[str] = Field(None, max_length=200)
    phone: Optional[str] = Field(None, max_length=20)


class UserPreferencesUpdate(BaseModel):
    """Schema for updating user preferences."""
    email_notifications: Optional[bool] = None
    push_notifications: Optional[bool] = None
    marketing_emails: Optional[bool] = None
    safety_reminders: Optional[bool] = None
    project_updates: Optional[bool] = None
    community_notifications: Optional[bool] = None
    preferred_units: Optional[str] = Field(None, regex="^(metric|imperial)$")
    timezone: Optional[str] = Field(None, max_length=50)
    language: Optional[str] = Field(None, max_length=10)


class UserStatsResponse(BaseModel):
    """Schema for user statistics response."""
    total_projects: int
    completed_projects: int
    in_progress_projects: int
    planned_projects: int
    completion_rate: float
    total_hours_logged: float
    total_cost_saved: float
    average_safety_score: float
    skill_level: str
    member_since: datetime
    last_active: Optional[datetime]
    achievements: List[Dict[str, Any]]


class UserProfileResponse(BaseModel):
    """Schema for user profile response."""
    id: str
    email: str
    first_name: str
    last_name: str
    username: Optional[str]
    location: Optional[str]
    skill_level: str
    bio: Optional[str]
    website: Optional[str]
    phone: Optional[str]
    avatar_url: Optional[str]
    is_premium: bool
    is_verified: bool
    created_at: datetime
    updated_at: datetime
    stats: UserStatsResponse


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """Dependency to get current authenticated user."""
    try:
        access_token = credentials.credentials
        return await auth_service.get_current_user(db, access_token)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )


@router.get("/me", response_model=UserProfileResponse)
@rate_limit(requests_per_minute=30, requests_per_hour=200)
async def get_current_user_profile(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get current user's profile information and statistics.
    
    Returns comprehensive user profile data including:
    - Basic profile information
    - Project statistics
    - Skill level and achievements
    - Account status and preferences
    """
    try:
        # Get user statistics
        stats_query = select(
            func.count(Project.id).label('total_projects'),
            func.count(Project.id).filter(Project.status == ProjectStatus.COMPLETED).label('completed_projects'),
            func.count(Project.id).filter(Project.status == ProjectStatus.IN_PROGRESS).label('in_progress_projects'),
            func.count(Project.id).filter(Project.status == ProjectStatus.PLANNING).label('planned_projects'),
            func.avg(Project.safety_score).label('avg_safety_score'),
            func.sum(Project.actual_cost).label('total_cost')
        ).where(Project.user_id == current_user.id)
        
        stats_result = await db.execute(stats_query)
        stats_row = stats_result.first()
        
        # Calculate completion rate
        total_projects = stats_row.total_projects or 0
        completed_projects = stats_row.completed_projects or 0
        completion_rate = (completed_projects / total_projects * 100) if total_projects > 0 else 0
        
        # Create user statistics
        user_stats = UserStatsResponse(
            total_projects=total_projects,
            completed_projects=completed_projects,
            in_progress_projects=stats_row.in_progress_projects or 0,
            planned_projects=stats_row.planned_projects or 0,
            completion_rate=round(completion_rate, 1),
            total_hours_logged=0.0,  # Placeholder - would calculate from project logs
            total_cost_saved=stats_row.total_cost or 0.0,
            average_safety_score=round(stats_row.avg_safety_score or 0, 1),
            skill_level=current_user.skill_level.value,
            member_since=current_user.created_at,
            last_active=current_user.last_active_at,
            achievements=[]  # Placeholder for achievements system
        )
        
        # Create profile response
        profile = UserProfileResponse(
            id=str(current_user.id),
            email=current_user.email,
            first_name=current_user.first_name,
            last_name=current_user.last_name,
            username=current_user.username,
            location=current_user.location,
            skill_level=current_user.skill_level.value,
            bio=current_user.bio,
            website=current_user.website,
            phone=current_user.phone,
            avatar_url=current_user.avatar_url,
            is_premium=current_user.is_premium,
            is_verified=current_user.is_verified,
            created_at=current_user.created_at,
            updated_at=current_user.updated_at,
            stats=user_stats
        )
        
        logging.info(f"User profile retrieved for user {current_user.id}")
        
        return profile
        
    except Exception as e:
        logging.error(f"User profile retrieval failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user profile"
        )


@router.patch("/me", response_model=UserProfileResponse)
@rate_limit(requests_per_minute=10, requests_per_hour=50)
async def update_user_profile(
    profile_data: UserProfileUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update current user's profile information.
    
    Allows users to update their profile details including:
    - Name and contact information
    - Location and skill level
    - Bio and website
    - Professional information
    """
    try:
        # Update user fields
        update_data = profile_data.dict(exclude_unset=True)
        
        for field, value in update_data.items():
            if hasattr(current_user, field):
                setattr(current_user, field, value)
        
        current_user.updated_at = datetime.utcnow()
        
        await db.commit()
        await db.refresh(current_user)
        
        logging.info(f"User profile updated for user {current_user.id}")
        
        # Return updated profile (reuse get_current_user_profile logic)
        return await get_current_user_profile(current_user, db)
        
    except Exception as e:
        await db.rollback()
        logging.error(f"User profile update failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user profile"
        )


@router.post("/me/avatar")
@rate_limit(requests_per_minute=5, requests_per_hour=20)
async def upload_avatar(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Upload user avatar image.
    
    Accepts image files and stores them securely with
    proper validation and resizing.
    """
    try:
        # Validate file type
        allowed_types = ['image/jpeg', 'image/png', 'image/webp']
        if file.content_type not in allowed_types:
            raise ValidationError(f"Invalid file type. Allowed types: {allowed_types}")
        
        # Validate file size (max 5MB)
        max_size = 5 * 1024 * 1024  # 5MB
        file_content = await file.read()
        if len(file_content) > max_size:
            raise ValidationError("File size too large. Maximum size is 5MB")
        
        # In production, this would:
        # 1. Upload to cloud storage (S3, CloudFlare, etc.)
        # 2. Resize and optimize the image
        # 3. Generate different sizes (thumbnail, medium, large)
        # 4. Update user's avatar_url
        
        # Placeholder implementation
        avatar_url = f"https://cdn.homecraft-intelligence.com/avatars/{current_user.id}.jpg"
        current_user.avatar_url = avatar_url
        current_user.updated_at = datetime.utcnow()
        
        await db.commit()
        
        logging.info(f"Avatar uploaded for user {current_user.id}")
        
        return {
            "message": "Avatar uploaded successfully",
            "avatar_url": avatar_url
        }
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        await db.rollback()
        logging.error(f"Avatar upload failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload avatar"
        )


@router.get("/me/preferences")
@rate_limit(requests_per_minute=20, requests_per_hour=100)
async def get_user_preferences(
    current_user: User = Depends(get_current_user)
):
    """
    Get current user's preferences and settings.
    
    Returns user preferences for notifications, units,
    privacy settings, and other customizable options.
    """
    try:
        # In production, preferences would be stored in a separate table
        # For now, return default preferences
        preferences = {
            "email_notifications": True,
            "push_notifications": True,
            "marketing_emails": False,
            "safety_reminders": True,
            "project_updates": True,
            "community_notifications": True,
            "preferred_units": "imperial",
            "timezone": "America/New_York",
            "language": "en",
            "privacy": {
                "profile_visibility": "public",
                "project_visibility": "public",
                "show_location": True,
                "show_email": False
            },
            "ai_settings": {
                "safety_level": "standard",
                "response_detail": "detailed",
                "include_alternatives": True
            }
        }
        
        logging.info(f"User preferences retrieved for user {current_user.id}")
        
        return preferences
        
    except Exception as e:
        logging.error(f"User preferences retrieval failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user preferences"
        )


@router.patch("/me/preferences")
@rate_limit(requests_per_minute=10, requests_per_hour=50)
async def update_user_preferences(
    preferences_data: UserPreferencesUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update current user's preferences and settings.
    
    Allows users to customize their experience including:
    - Notification preferences
    - Unit preferences (metric/imperial)
    - Privacy settings
    - AI assistant behavior
    """
    try:
        # In production, this would update a preferences table
        # For now, just log the update
        update_data = preferences_data.dict(exclude_unset=True)
        
        logging.info(f"User preferences updated for user {current_user.id}: {update_data}")
        
        return {
            "message": "Preferences updated successfully",
            "updated_fields": list(update_data.keys())
        }
        
    except Exception as e:
        logging.error(f"User preferences update failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user preferences"
        )


@router.delete("/me")
@rate_limit(requests_per_minute=2, requests_per_hour=5)
async def delete_user_account(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete current user's account and all associated data.
    
    This is a permanent action that removes:
    - User profile and authentication data
    - All projects and associated data
    - Preferences and settings
    - Uploaded files and images
    
    This action cannot be undone.
    """
    try:
        # In production, this would:
        # 1. Soft delete the user (mark as deleted)
        # 2. Schedule background job to clean up data
        # 3. Remove from search indexes
        # 4. Delete uploaded files
        # 5. Send confirmation email
        
        # For now, just mark as deleted
        current_user.is_active = False
        current_user.deleted_at = datetime.utcnow()
        current_user.updated_at = datetime.utcnow()
        
        await db.commit()
        
        logging.info(f"User account deleted: {current_user.id}")
        
        return {
            "message": "Account deleted successfully",
            "deleted_at": current_user.deleted_at.isoformat()
        }
        
    except Exception as e:
        await db.rollback()
        logging.error(f"User account deletion failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete user account"
        )


@router.get("/me/activity")
@rate_limit(requests_per_minute=15, requests_per_hour=100)
async def get_user_activity(
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get current user's recent activity and timeline.
    
    Returns a chronological list of user activities including:
    - Project creation and updates
    - Task completions
    - Safety assessments
    - Community interactions
    """
    try:
        # In production, this would query an activity log table
        # For now, return mock activity data
        activities = [
            {
                "id": "activity_1",
                "type": "project_created",
                "title": "Created new project: Kitchen Cabinet Installation",
                "description": "Started planning a kitchen renovation project",
                "timestamp": "2024-01-15T10:30:00Z",
                "metadata": {
                    "project_id": "proj_123",
                    "project_type": "kitchen"
                }
            },
            {
                "id": "activity_2",
                "type": "safety_assessment",
                "title": "Completed safety assessment",
                "description": "Safety score: 85/100 for electrical work",
                "timestamp": "2024-01-14T15:45:00Z",
                "metadata": {
                    "safety_score": 85,
                    "project_type": "electrical"
                }
            },
            {
                "id": "activity_3",
                "type": "task_completed",
                "title": "Completed task: Measure kitchen space",
                "description": "Finished measuring and planning phase",
                "timestamp": "2024-01-13T09:15:00Z",
                "metadata": {
                    "task_id": "task_456",
                    "project_id": "proj_123"
                }
            }
        ]
        
        # Limit results
        activities = activities[:limit]
        
        logging.info(f"User activity retrieved for user {current_user.id}")
        
        return {
            "activities": activities,
            "total": len(activities),
            "limit": limit
        }
        
    except Exception as e:
        logging.error(f"User activity retrieval failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user activity"
        )
