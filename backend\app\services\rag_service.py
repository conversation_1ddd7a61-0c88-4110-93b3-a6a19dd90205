"""
HomeCraft Intelligence - RAG Service

This module implements the Retrieval-Augmented Generation (RAG) pipeline
for providing intelligent, context-aware responses to user queries about
home improvement projects, safety guidelines, and DIY instructions.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import asyncio
import json

from app.config import settings
from app.services.embedding_service import EmbeddingService
from app.services.llm_service import LLMService
from app.services.safety_service import SafetyService
from app.core.exceptions import RAGError


class RAGService:
    """
    Service class for handling RAG operations including query processing,
    context retrieval, response generation, and safety validation.
    """
    
    def __init__(self):
        self.embedding_service = EmbeddingService()
        self.llm_service = LLMService()
        self.safety_service = SafetyService()
        self.chunk_size = settings.RAG_CHUNK_SIZE
        self.chunk_overlap = settings.RAG_CHUNK_OVERLAP
        self.top_k = settings.RAG_TOP_K
        self.similarity_threshold = settings.RAG_SIMILARITY_THRESHOLD
        
    async def initialize(self):
        """Initialize all RAG components."""
        await self.embedding_service.initialize()
        await self.llm_service.initialize()
        logging.info("RAG service initialized successfully")
    
    async def process_query(
        self,
        query: str,
        context: Dict[str, Any] = None,
        user_id: str = None,
        include_safety: bool = True,
        include_materials: bool = False
    ) -> Dict[str, Any]:
        """
        Process a user query through the complete RAG pipeline.
        
        Args:
            query: User's question or request
            context: Additional context (project details, user preferences, etc.)
            user_id: User identifier for personalization
            include_safety: Whether to include safety warnings
            include_materials: Whether to suggest materials
            
        Returns:
            Dict containing response, sources, safety warnings, and suggestions
        """
        try:
            start_time = datetime.utcnow()
            
            # Step 1: Query preprocessing and enhancement
            enhanced_query = await self._enhance_query(query, context)
            
            # Step 2: Retrieve relevant context from knowledge base
            retrieved_contexts = await self._retrieve_context(enhanced_query, context)
            
            # Step 3: Generate response using LLM
            response = await self._generate_response(
                query, enhanced_query, retrieved_contexts, context
            )
            
            # Step 4: Safety validation and warnings
            safety_warnings = []
            if include_safety:
                safety_warnings = await self._get_safety_warnings(
                    query, response, context
                )
            
            # Step 5: Material suggestions (if requested)
            materials_suggested = []
            if include_materials:
                materials_suggested = await self._suggest_materials(
                    query, response, context
                )
            
            # Step 6: Generate follow-up questions
            follow_up_questions = await self._generate_follow_ups(
                query, response, context
            )
            
            # Step 7: Compile final response
            result = {
                "response": response["text"],
                "confidence_score": response.get("confidence", 0.8),
                "sources": [
                    {
                        "title": ctx["title"],
                        "url": ctx.get("url", "internal://knowledge-base"),
                        "relevance_score": ctx["score"],
                        "snippet": ctx["content"][:200] + "..." if len(ctx["content"]) > 200 else ctx["content"]
                    }
                    for ctx in retrieved_contexts[:3]  # Top 3 sources
                ],
                "safety_warnings": safety_warnings,
                "materials_suggested": materials_suggested,
                "follow_up_questions": follow_up_questions,
                "processing_time": (datetime.utcnow() - start_time).total_seconds(),
                "metadata": {
                    "query_enhanced": enhanced_query != query,
                    "contexts_retrieved": len(retrieved_contexts),
                    "safety_checked": include_safety,
                    "materials_included": include_materials
                }
            }
            
            # Log query for analytics
            await self._log_query(user_id, query, result)
            
            return result
            
        except Exception as e:
            logging.error(f"RAG query processing failed: {e}")
            raise RAGError(f"Failed to process query: {str(e)}")
    
    async def _enhance_query(self, query: str, context: Dict[str, Any] = None) -> str:
        """Enhance the user query with additional context and keywords."""
        try:
            # Add context information to the query
            enhanced_parts = [query]
            
            if context:
                # Add skill level context
                if "skill_level" in context:
                    enhanced_parts.append(f"skill level: {context['skill_level']}")
                
                # Add project context
                if "project_type" in context:
                    enhanced_parts.append(f"project type: {context['project_type']}")
                
                # Add room/location context
                if "room_type" in context:
                    enhanced_parts.append(f"room: {context['room_type']}")
                
                # Add location for building codes
                if "location" in context:
                    enhanced_parts.append(f"location: {context['location']}")
            
            enhanced_query = " | ".join(enhanced_parts)
            
            # Use LLM to further enhance the query if needed
            if len(enhanced_query) > len(query) * 1.5:  # Significant context added
                enhancement_prompt = f"""
                Enhance this DIY home improvement query for better search results:
                Original: {query}
                Context: {' | '.join(enhanced_parts[1:])}
                
                Provide a clear, searchable query that includes relevant keywords:
                """
                
                enhanced_response = await self.llm_service.generate_response(
                    enhancement_prompt,
                    max_tokens=100,
                    temperature=0.3
                )
                
                if enhanced_response and len(enhanced_response.strip()) > 10:
                    return enhanced_response.strip()
            
            return enhanced_query
            
        except Exception as e:
            logging.warning(f"Query enhancement failed: {e}")
            return query
    
    async def _retrieve_context(
        self, 
        query: str, 
        context: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """Retrieve relevant context from the knowledge base."""
        try:
            # Generate query embedding
            query_embedding = await self.embedding_service.embed_text(query)
            
            # Search vector database
            search_results = await self.embedding_service.search_similar(
                query_embedding,
                top_k=self.top_k * 2,  # Get more results for filtering
                threshold=self.similarity_threshold
            )
            
            # Filter and rank results based on context
            filtered_results = await self._filter_and_rank_results(
                search_results, context
            )
            
            return filtered_results[:self.top_k]
            
        except Exception as e:
            logging.error(f"Context retrieval failed: {e}")
            return []
    
    async def _filter_and_rank_results(
        self,
        results: List[Dict[str, Any]],
        context: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """Filter and rank search results based on context and relevance."""
        if not context:
            return results
        
        # Apply context-based filtering
        filtered_results = []
        
        for result in results:
            score = result["score"]
            metadata = result.get("metadata", {})
            
            # Boost score based on context matching
            if context.get("skill_level") == metadata.get("skill_level"):
                score += 0.1
            
            if context.get("project_type") == metadata.get("project_type"):
                score += 0.15
            
            if context.get("room_type") == metadata.get("room_type"):
                score += 0.1
            
            # Safety content gets priority
            if metadata.get("category") == "safety":
                score += 0.2
            
            result["score"] = min(score, 1.0)  # Cap at 1.0
            filtered_results.append(result)
        
        # Sort by adjusted score
        filtered_results.sort(key=lambda x: x["score"], reverse=True)
        
        return filtered_results
    
    async def _generate_response(
        self,
        original_query: str,
        enhanced_query: str,
        contexts: List[Dict[str, Any]],
        user_context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Generate response using LLM with retrieved context."""
        try:
            # Prepare context for LLM
            context_text = "\n\n".join([
                f"Source: {ctx['title']}\nContent: {ctx['content']}"
                for ctx in contexts
            ])
            
            # Build prompt
            prompt = self._build_response_prompt(
                original_query, context_text, user_context
            )
            
            # Generate response
            response_text = await self.llm_service.generate_response(
                prompt,
                max_tokens=settings.LLM_MAX_TOKENS,
                temperature=settings.LLM_TEMPERATURE
            )
            
            # Calculate confidence based on context quality
            confidence = self._calculate_confidence(contexts, response_text)
            
            return {
                "text": response_text,
                "confidence": confidence,
                "prompt_tokens": len(prompt.split()),
                "response_tokens": len(response_text.split())
            }
            
        except Exception as e:
            logging.error(f"Response generation failed: {e}")
            return {
                "text": "I apologize, but I'm having trouble generating a response right now. Please try rephrasing your question or contact support if the issue persists.",
                "confidence": 0.0,
                "error": str(e)
            }
    
    def _build_response_prompt(
        self,
        query: str,
        context: str,
        user_context: Dict[str, Any] = None
    ) -> str:
        """Build the prompt for LLM response generation."""
        skill_level = user_context.get("skill_level", "beginner") if user_context else "beginner"
        
        prompt = f"""You are HomeCraft Intelligence, an expert DIY home improvement assistant. 
Your role is to provide safe, accurate, and helpful guidance for home improvement projects.

User Query: {query}

Relevant Information:
{context}

User Context:
- Skill Level: {skill_level}
- Safety Priority: High (always emphasize safety)

Instructions:
1. Provide a clear, step-by-step answer based on the relevant information
2. Adapt your language to the user's skill level
3. Always prioritize safety - mention safety precautions prominently
4. If the task requires professional help, clearly state this
5. Be specific about tools and materials needed
6. Include relevant building codes or permits if applicable
7. Keep your response practical and actionable

Response:"""
        
        return prompt
    
    def _calculate_confidence(
        self, 
        contexts: List[Dict[str, Any]], 
        response: str
    ) -> float:
        """Calculate confidence score for the response."""
        if not contexts:
            return 0.3
        
        # Base confidence from context quality
        avg_score = sum(ctx["score"] for ctx in contexts) / len(contexts)
        
        # Adjust based on response characteristics
        confidence = avg_score
        
        # Boost if response mentions safety
        if any(word in response.lower() for word in ["safety", "caution", "warning", "careful"]):
            confidence += 0.1
        
        # Boost if response is detailed (good length)
        if 100 < len(response) < 1000:
            confidence += 0.1
        
        # Reduce if response is too short or too long
        if len(response) < 50 or len(response) > 2000:
            confidence -= 0.2
        
        return max(0.0, min(1.0, confidence))
    
    async def _get_safety_warnings(
        self,
        query: str,
        response: Dict[str, Any],
        context: Dict[str, Any] = None
    ) -> List[str]:
        """Get safety warnings for the query and response."""
        try:
            return await self.safety_service.get_safety_warnings(
                query, response["text"], context
            )
        except Exception as e:
            logging.error(f"Safety warning generation failed: {e}")
            return ["Always prioritize safety and consult professionals when in doubt."]
    
    async def _suggest_materials(
        self,
        query: str,
        response: Dict[str, Any],
        context: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """Suggest materials based on the query and response."""
        # This would integrate with material service
        # For now, return empty list
        return []
    
    async def _generate_follow_ups(
        self,
        query: str,
        response: Dict[str, Any],
        context: Dict[str, Any] = None
    ) -> List[str]:
        """Generate follow-up questions."""
        try:
            follow_up_prompt = f"""
            Based on this DIY question and answer, suggest 2-3 relevant follow-up questions:
            
            Question: {query}
            Answer: {response['text'][:500]}...
            
            Generate practical follow-up questions that a DIY enthusiast might ask:
            """
            
            follow_ups_text = await self.llm_service.generate_response(
                follow_up_prompt,
                max_tokens=200,
                temperature=0.8
            )
            
            # Parse follow-up questions (simple line-based parsing)
            follow_ups = [
                line.strip().lstrip("123456789.-• ")
                for line in follow_ups_text.split("\n")
                if line.strip() and "?" in line
            ]
            
            return follow_ups[:3]  # Return max 3 follow-ups
            
        except Exception as e:
            logging.error(f"Follow-up generation failed: {e}")
            return []
    
    async def _log_query(
        self,
        user_id: str,
        query: str,
        result: Dict[str, Any]
    ) -> None:
        """Log query for analytics and improvement."""
        try:
            # This would typically save to database
            log_data = {
                "user_id": user_id,
                "query": query,
                "response_length": len(result["response"]),
                "confidence_score": result["confidence_score"],
                "processing_time": result["processing_time"],
                "sources_count": len(result["sources"]),
                "timestamp": datetime.utcnow().isoformat()
            }
            
            logging.info(f"RAG Query logged: {json.dumps(log_data)}")
            
        except Exception as e:
            logging.error(f"Query logging failed: {e}")


# Global RAG service instance
rag_service = RAGService()
