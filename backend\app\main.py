"""
HomeCraft Intelligence - FastAPI Main Application

This module initializes the FastAPI application with all necessary middleware,
routers, and configuration for the HomeCraft Intelligence platform.
"""

from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
import logging
import time
import uvicorn

from app.config import settings
from app.core.logging import setup_logging
from app.core.exceptions import HomeCraftException
from app.middleware.rate_limit import rate_limiter, rate_limit_middleware
from app.api.v1 import auth, projects, rag, materials, safety, users, community
from app.database import init_db


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown events."""
    # Startup
    setup_logging()
    logging.info("Starting HomeCraft Intelligence API...")
    
    # Initialize database
    await init_db()
    logging.info("Database initialized successfully")
    
    # Initialize rate limiter
    try:
        await rate_limiter.initialize()
        logging.info("Rate limiter initialized successfully")
    except Exception as e:
        logging.warning(f"Rate limiter initialization failed: {e}")

    # Initialize AI services (optional for basic functionality)
    try:
        from app.services.embedding_service import embedding_service
        from app.services.llm_service import llm_service
        from app.services.safety_service import safety_service
        from app.services.material_service import material_service

        await embedding_service.initialize()
        await llm_service.initialize()
        await safety_service.initialize()
        await material_service.initialize()

        logging.info("AI services initialized successfully")
    except Exception as e:
        logging.warning(f"AI services initialization failed: {e} - Running in basic mode")
    
    yield
    
    # Shutdown
    logging.info("Shutting down HomeCraft Intelligence API...")
    try:
        await embedding_service.cleanup()
        await llm_service.cleanup()
        await safety_service.cleanup()
        await material_service.cleanup()
    except Exception as e:
        logging.warning(f"Service cleanup failed: {e}")


# Create FastAPI application
app = FastAPI(
    title="HomeCraft Intelligence API",
    description="AI-Powered DIY Home Improvement Assistant API",
    version="1.0.0",
    docs_url="/docs" if settings.ENVIRONMENT != "production" else None,
    redoc_url="/redoc" if settings.ENVIRONMENT != "production" else None,
    openapi_url="/openapi.json" if settings.ENVIRONMENT != "production" else None,
    lifespan=lifespan
)

# Security middleware
app.add_middleware(TrustedHostMiddleware, allowed_hosts=settings.ALLOWED_HOSTS.split(","))

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS.split(","),
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH"],
    allow_headers=["*"],
)

# Rate limiting middleware
app.middleware("http")(rate_limit_middleware)


# Exception handlers
@app.exception_handler(HomeCraftException)
async def homecraft_exception_handler(request: Request, exc: HomeCraftException):
    """Handle custom HomeCraft exceptions."""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "detail": exc.detail,
            "error_code": exc.error_code,
            "timestamp": time.time()
        }
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Handle request validation errors."""
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "detail": "Validation error",
            "errors": [
                {
                    "field": ".".join(str(loc) for loc in error["loc"]),
                    "message": error["msg"],
                    "type": error["type"]
                }
                for error in exc.errors()
            ]
        }
    )


@app.exception_handler(500)
async def internal_server_error_handler(request: Request, exc: Exception):
    """Handle internal server errors."""
    logging.error(f"Internal server error: {exc}", exc_info=True)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "detail": "Internal server error",
            "error_id": str(time.time())  # Simple error ID for tracking
        }
    )


# Health check endpoint
@app.get("/health", tags=["Health"])
async def health_check():
    """Health check endpoint for monitoring."""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT
    }


# Root endpoint
@app.get("/", tags=["Root"])
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Welcome to HomeCraft Intelligence API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }


# Include API routers
app.include_router(
    auth.router,
    prefix="/api/v1/auth",
    tags=["Authentication"]
)

app.include_router(
    users.router,
    prefix="/api/v1/users",
    tags=["Users"]
)

app.include_router(
    projects.router,
    prefix="/api/v1/projects",
    tags=["Projects"]
)

app.include_router(
    rag.router,
    prefix="/api/v1/rag",
    tags=["RAG Assistant"]
)

app.include_router(
    materials.router,
    prefix="/api/v1/materials",
    tags=["Materials"]
)

app.include_router(
    safety.router,
    prefix="/api/v1/safety",
    tags=["Safety"]
)

app.include_router(
    community.router,
    prefix="/api/v1/community",
    tags=["Community"]
)


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.ENVIRONMENT == "development",
        log_level="info"
    )
