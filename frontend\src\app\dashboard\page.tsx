'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
  PlusIcon,
  HomeIcon,
  WrenchScrewdriverIcon,
  ChatBubbleLeftRightIcon,
  ChartBarIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth, withAuth } from '@/lib/auth-context';
import { formatDate, formatPercentage } from '@/lib/utils';

// Mock data - replace with actual API calls
const mockProjects = [
  {
    id: '1',
    title: 'Kitchen Cabinet Installation',
    description: 'Installing new kitchen cabinets and countertops',
    status: 'in_progress',
    progress: 65,
    estimated_completion: '2024-02-15',
    safety_score: 85,
    created_at: '2024-01-10',
  },
  {
    id: '2',
    title: 'Bathroom Tile Replacement',
    description: 'Replacing old bathroom tiles with new ceramic tiles',
    status: 'planning',
    progress: 15,
    estimated_completion: '2024-03-01',
    safety_score: 92,
    created_at: '2024-01-20',
  },
  {
    id: '3',
    title: 'Living Room Paint Job',
    description: 'Painting living room walls and ceiling',
    status: 'completed',
    progress: 100,
    estimated_completion: '2024-01-25',
    safety_score: 98,
    created_at: '2024-01-05',
  },
];

const mockStats = {
  totalProjects: 12,
  completedProjects: 8,
  inProgressProjects: 3,
  plannedProjects: 1,
  totalSavings: 4250,
  averageSafetyScore: 89,
};

const quickActions = [
  {
    title: 'Start New Project',
    description: 'Get AI-powered project planning',
    icon: PlusIcon,
    href: '/projects/new',
    color: 'bg-blue-500',
  },
  {
    title: 'Ask AI Assistant',
    description: 'Get instant DIY advice',
    icon: ChatBubbleLeftRightIcon,
    href: '/assistant',
    color: 'bg-purple-500',
  },
  {
    title: 'Material Calculator',
    description: 'Calculate materials and costs',
    icon: ChartBarIcon,
    href: '/materials',
    color: 'bg-green-500',
  },
  {
    title: 'Safety Check',
    description: 'Verify project safety',
    icon: ExclamationTriangleIcon,
    href: '/safety',
    color: 'bg-orange-500',
  },
];

function DashboardPage() {
  const { user } = useAuth();
  const [projects, setProjects] = useState(mockProjects);
  const [stats, setStats] = useState(mockStats);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // TODO: Fetch real data from API
    // fetchDashboardData();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'in_progress':
        return 'text-blue-600 bg-blue-100';
      case 'planning':
        return 'text-yellow-600 bg-yellow-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return CheckCircleIcon;
      case 'in_progress':
        return ClockIcon;
      case 'planning':
        return WrenchScrewdriverIcon;
      default:
        return HomeIcon;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Welcome back, {user?.first_name}!
                </h1>
                <p className="text-gray-600">
                  Ready to tackle your next DIY project?
                </p>
              </div>
              <Button asChild>
                <Link href="/projects/new">
                  <PlusIcon className="h-4 w-4 mr-2" />
                  New Project
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <HomeIcon className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Projects</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.totalProjects}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <CheckCircleIcon className="h-8 w-8 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Completed</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.completedProjects}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <ChartBarIcon className="h-8 w-8 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Money Saved</p>
                    <p className="text-2xl font-bold text-gray-900">${stats.totalSavings.toLocaleString()}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <ExclamationTriangleIcon className="h-8 w-8 text-orange-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Safety Score</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.averageSafetyScore}%</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Recent Projects */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Recent Projects</CardTitle>
                <CardDescription>
                  Your latest DIY projects and their progress
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {projects.map((project, index) => {
                    const StatusIcon = getStatusIcon(project.status);
                    return (
                      <motion.div
                        key={project.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="flex items-center space-x-4 p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex-shrink-0">
                          <StatusIcon className="h-6 w-6 text-gray-400" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {project.title}
                            </p>
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                              {project.status.replace('_', ' ')}
                            </span>
                          </div>
                          <p className="text-sm text-gray-500 truncate">
                            {project.description}
                          </p>
                          <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                            <span>Progress: {project.progress}%</span>
                            <span>Safety: {project.safety_score}%</span>
                            <span>Due: {formatDate(project.estimated_completion)}</span>
                          </div>
                          {/* Progress Bar */}
                          <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${project.progress}%` }}
                            />
                          </div>
                        </div>
                        <div className="flex-shrink-0">
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/projects/${project.id}`}>
                              View
                            </Link>
                          </Button>
                        </div>
                      </motion.div>
                    );
                  })}
                </div>
                <div className="mt-6">
                  <Button variant="outline" className="w-full" asChild>
                    <Link href="/projects">
                      View All Projects
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Jump into your most common tasks
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {quickActions.map((action, index) => (
                    <motion.div
                      key={action.title}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5 + index * 0.1 }}
                    >
                      <Link
                        href={action.href}
                        className="flex items-center p-3 rounded-lg border hover:bg-gray-50 transition-colors group"
                      >
                        <div className={`flex-shrink-0 p-2 rounded-lg ${action.color}`}>
                          <action.icon className="h-5 w-5 text-white" />
                        </div>
                        <div className="ml-3 flex-1">
                          <p className="text-sm font-medium text-gray-900 group-hover:text-blue-600">
                            {action.title}
                          </p>
                          <p className="text-xs text-gray-500">
                            {action.description}
                          </p>
                        </div>
                      </Link>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* User Progress */}
            <Card>
              <CardHeader>
                <CardTitle>Your Progress</CardTitle>
                <CardDescription>
                  Track your DIY journey
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm">
                      <span>Skill Level</span>
                      <span className="font-medium capitalize">{user?.skill_level}</span>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between text-sm">
                      <span>Completion Rate</span>
                      <span className="font-medium">{formatPercentage(user?.completion_rate || 0)}</span>
                    </div>
                    <div className="mt-1 w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-600 h-2 rounded-full"
                        style={{ width: `${user?.completion_rate || 0}%` }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between text-sm">
                      <span>Projects Completed</span>
                      <span className="font-medium">{user?.completed_projects || 0}</span>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between text-sm">
                      <span>Member Since</span>
                      <span className="font-medium">{formatDate(user?.created_at || new Date())}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

export default withAuth(DashboardPage);
