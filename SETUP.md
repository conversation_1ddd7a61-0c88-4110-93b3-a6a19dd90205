# HomeCraft Intelligence - Setup Instructions

This guide will help you set up and run the HomeCraft Intelligence application locally for development or deploy it to production.

## Prerequisites

### Required Software
- **Node.js** (v18 or higher) - [Download](https://nodejs.org/)
- **Python** (v3.11 or higher) - [Download](https://python.org/)
- **PostgreSQL** (v15 or higher) - [Download](https://postgresql.org/)
- **Redis** (v7 or higher) - [Download](https://redis.io/)
- **Docker & Docker Compose** (optional, for containerized setup) - [Download](https://docker.com/)

### AI/ML Services
- **Ollama** (for local LLM) - [Download](https://ollama.ai/)
- **Chroma** (vector database) - Included in Docker setup

## Quick Start with Docker (Recommended)

### 1. Clone the Repository
```bash
git clone https://github.com/HectorTa1989/homecraft-intelligence.git
cd homecraft-intelligence
```

### 2. Environment Configuration
```bash
# Copy environment files
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env.local

# Edit the environment files with your configuration
# At minimum, set these variables:
# - SECRET_KEY (generate a secure random string)
# - POSTGRES_PASSWORD
# - REDIS_PASSWORD
# - NEXTAUTH_SECRET
```

### 3. Start Services
```bash
# Start all services
docker-compose up -d

# Start with monitoring (optional)
docker-compose --profile monitoring up -d

# View logs
docker-compose logs -f
```

### 4. Initialize the Database
```bash
# Run database migrations
docker-compose exec backend alembic upgrade head

# Seed initial data (optional)
docker-compose exec backend python scripts/seed_data.py
```

### 5. Download AI Models
```bash
# Download Ollama model
docker-compose exec ollama ollama pull llama2:7b

# Initialize Chroma collection
docker-compose exec backend python scripts/init_vector_db.py
```

### 6. Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Grafana (monitoring)**: http://localhost:3001 (admin/admin)

## Manual Setup (Development)

### Backend Setup

1. **Create Virtual Environment**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. **Install Dependencies**
```bash
pip install -r requirements.txt
```

3. **Database Setup**
```bash
# Start PostgreSQL and create database
createdb homecraft_intelligence

# Install pgvector extension
psql -d homecraft_intelligence -c "CREATE EXTENSION IF NOT EXISTS vector;"

# Run migrations
alembic upgrade head
```

4. **Start Redis**
```bash
redis-server
```

5. **Start Chroma**
```bash
chroma run --host localhost --port 8001
```

6. **Start Ollama**
```bash
ollama serve
ollama pull llama2:7b
```

7. **Start Backend**
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Frontend Setup

1. **Install Dependencies**
```bash
cd frontend
npm install
```

2. **Start Development Server**
```bash
npm run dev
```

## Configuration

### Backend Environment Variables

Key variables to configure in `backend/.env`:

```env
# Database
DATABASE_URL=postgresql://postgres:password@localhost:5432/homecraft_intelligence

# Redis
REDIS_URL=redis://localhost:6379/0

# Security
SECRET_KEY=your-very-long-random-secret-key

# AI Services
CHROMA_HOST=localhost
CHROMA_PORT=8001
OLLAMA_BASE_URL=http://localhost:11434
LLM_MODEL=llama2:7b

# External APIs (optional)
OPENAI_API_KEY=your-openai-key
BUILDING_CODES_API_KEY=your-building-codes-key
MATERIAL_PRICES_API_KEY=your-material-prices-key
```

### Frontend Environment Variables

Key variables to configure in `frontend/.env.local`:

```env
# API
NEXT_PUBLIC_API_URL=http://localhost:8000

# Authentication
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret

# Analytics (optional)
NEXT_PUBLIC_GA_ID=your-google-analytics-id
```

## Testing

### Backend Tests
```bash
cd backend
pytest tests/ -v
pytest tests/ --cov=app --cov-report=html
```

### Frontend Tests
```bash
cd frontend
npm test
npm run test:coverage
npm run test:e2e
```

## Production Deployment

### 1. Environment Setup
- Set `ENVIRONMENT=production` in backend
- Set `NODE_ENV=production` in frontend
- Use strong passwords and secrets
- Configure SSL certificates

### 2. Database Migration
```bash
# Run migrations in production
docker-compose exec backend alembic upgrade head
```

### 3. SSL Configuration
- Update `nginx/nginx.conf` with your SSL certificates
- Ensure HTTPS redirects are configured

### 4. Monitoring
```bash
# Start with monitoring
docker-compose --profile monitoring up -d
```

### 5. Health Checks
- Backend: `GET /health`
- Frontend: `GET /api/health`
- Database: Check connection status
- Redis: Check ping response

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Ensure PostgreSQL is running
   - Check DATABASE_URL format
   - Verify database exists and user has permissions

2. **Redis Connection Error**
   - Ensure Redis is running
   - Check REDIS_URL format
   - Verify Redis password if set

3. **Ollama Model Not Found**
   - Pull the model: `ollama pull llama2:7b`
   - Check model name in configuration

4. **Chroma Connection Error**
   - Ensure Chroma is running on correct port
   - Check firewall settings

5. **Frontend API Errors**
   - Verify NEXT_PUBLIC_API_URL points to backend
   - Check CORS configuration in backend

### Logs and Debugging

```bash
# Docker logs
docker-compose logs backend
docker-compose logs frontend

# Application logs
tail -f backend/logs/homecraft.log

# Database logs
docker-compose logs postgres
```

## Development Workflow

### 1. Code Changes
- Backend: Auto-reload with `--reload` flag
- Frontend: Hot reload with Next.js dev server

### 2. Database Changes
```bash
# Create migration
alembic revision --autogenerate -m "Description"

# Apply migration
alembic upgrade head
```

### 3. Adding Dependencies
```bash
# Backend
pip install package-name
pip freeze > requirements.txt

# Frontend
npm install package-name
```

### 4. Running Tests
```bash
# Run all tests
make test

# Run specific test suite
pytest tests/test_auth.py
npm test -- --testNamePattern="Login"
```

## Support

For issues and questions:
- **Documentation**: Check the README.md and API docs
- **Issues**: Create a GitHub issue
- **Email**: <EMAIL>

## Security

- Keep dependencies updated
- Use environment variables for secrets
- Enable HTTPS in production
- Regular security audits
- Monitor logs for suspicious activity
