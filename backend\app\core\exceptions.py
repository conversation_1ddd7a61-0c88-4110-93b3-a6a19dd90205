"""
HomeCraft Intelligence - Custom Exception Classes

This module defines custom exception classes for the HomeCraft Intelligence application
to provide better error handling and user-friendly error messages.

Author: HomeCraft Intelligence Team
License: MIT
"""

from typing import Any, Dict, Optional
from fastapi import HTTPException, status


class HomeCraftException(Exception):
    """Base exception class for HomeCraft Intelligence application."""
    
    def __init__(
        self,
        message: str,
        error_code: str = "HOMECRAFT_ERROR",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)
    
    @property
    def detail(self) -> str:
        """Get the error detail message."""
        return self.message
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary format."""
        return {
            "error_code": self.error_code,
            "message": self.message,
            "status_code": self.status_code,
            "details": self.details
        }


class ValidationError(HomeCraftException):
    """Exception raised for validation errors."""
    
    def __init__(
        self,
        message: str,
        field: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details=details or {}
        )
        if field:
            self.details["field"] = field


class AuthenticationError(HomeCraftException):
    """Exception raised for authentication failures."""
    
    def __init__(
        self,
        message: str = "Authentication failed",
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_ERROR",
            status_code=status.HTTP_401_UNAUTHORIZED,
            details=details or {}
        )


class AuthorizationError(HomeCraftException):
    """Exception raised for authorization failures."""
    
    def __init__(
        self,
        message: str = "Access denied",
        required_permission: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            error_code="AUTHORIZATION_ERROR",
            status_code=status.HTTP_403_FORBIDDEN,
            details=details or {}
        )
        if required_permission:
            self.details["required_permission"] = required_permission


class NotFoundError(HomeCraftException):
    """Exception raised when a resource is not found."""
    
    def __init__(
        self,
        message: str = "Resource not found",
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            error_code="NOT_FOUND_ERROR",
            status_code=status.HTTP_404_NOT_FOUND,
            details=details or {}
        )
        if resource_type:
            self.details["resource_type"] = resource_type
        if resource_id:
            self.details["resource_id"] = resource_id


class ConflictError(HomeCraftException):
    """Exception raised for resource conflicts."""
    
    def __init__(
        self,
        message: str = "Resource conflict",
        conflicting_field: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            error_code="CONFLICT_ERROR",
            status_code=status.HTTP_409_CONFLICT,
            details=details or {}
        )
        if conflicting_field:
            self.details["conflicting_field"] = conflicting_field


class RateLimitError(HomeCraftException):
    """Exception raised when rate limits are exceeded."""
    
    def __init__(
        self,
        message: str = "Rate limit exceeded",
        retry_after: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            error_code="RATE_LIMIT_ERROR",
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            details=details or {}
        )
        if retry_after:
            self.details["retry_after"] = retry_after


class DatabaseError(HomeCraftException):
    """Exception raised for database-related errors."""
    
    def __init__(
        self,
        message: str = "Database operation failed",
        operation: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            error_code="DATABASE_ERROR",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=details or {}
        )
        if operation:
            self.details["operation"] = operation


class ExternalServiceError(HomeCraftException):
    """Exception raised for external service failures."""
    
    def __init__(
        self,
        message: str = "External service unavailable",
        service_name: Optional[str] = None,
        service_status: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            error_code="EXTERNAL_SERVICE_ERROR",
            status_code=status.HTTP_502_BAD_GATEWAY,
            details=details or {}
        )
        if service_name:
            self.details["service_name"] = service_name
        if service_status:
            self.details["service_status"] = service_status


# AI/ML Service Exceptions

class EmbeddingError(HomeCraftException):
    """Exception raised for embedding generation failures."""
    
    def __init__(
        self,
        message: str = "Embedding generation failed",
        model_name: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            error_code="EMBEDDING_ERROR",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=details or {}
        )
        if model_name:
            self.details["model_name"] = model_name


class VectorSearchError(HomeCraftException):
    """Exception raised for vector search failures."""
    
    def __init__(
        self,
        message: str = "Vector search failed",
        collection_name: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            error_code="VECTOR_SEARCH_ERROR",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=details or {}
        )
        if collection_name:
            self.details["collection_name"] = collection_name


class LLMError(HomeCraftException):
    """Exception raised for LLM service failures."""
    
    def __init__(
        self,
        message: str = "LLM service failed",
        provider: Optional[str] = None,
        model: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            error_code="LLM_ERROR",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=details or {}
        )
        if provider:
            self.details["provider"] = provider
        if model:
            self.details["model"] = model


class RAGError(HomeCraftException):
    """Exception raised for RAG pipeline failures."""
    
    def __init__(
        self,
        message: str = "RAG pipeline failed",
        stage: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            error_code="RAG_ERROR",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=details or {}
        )
        if stage:
            self.details["stage"] = stage


# Domain-Specific Exceptions

class SafetyError(HomeCraftException):
    """Exception raised for safety service failures."""
    
    def __init__(
        self,
        message: str = "Safety assessment failed",
        safety_level: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            error_code="SAFETY_ERROR",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=details or {}
        )
        if safety_level:
            self.details["safety_level"] = safety_level


class ComplianceError(HomeCraftException):
    """Exception raised for building code compliance failures."""
    
    def __init__(
        self,
        message: str = "Compliance check failed",
        code_type: Optional[str] = None,
        location: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            error_code="COMPLIANCE_ERROR",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=details or {}
        )
        if code_type:
            self.details["code_type"] = code_type
        if location:
            self.details["location"] = location


class MaterialError(HomeCraftException):
    """Exception raised for material service failures."""
    
    def __init__(
        self,
        message: str = "Material service failed",
        material_type: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            error_code="MATERIAL_ERROR",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=details or {}
        )
        if material_type:
            self.details["material_type"] = material_type


class CalculationError(HomeCraftException):
    """Exception raised for calculation failures."""
    
    def __init__(
        self,
        message: str = "Calculation failed",
        calculation_type: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            error_code="CALCULATION_ERROR",
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details=details or {}
        )
        if calculation_type:
            self.details["calculation_type"] = calculation_type


class ProjectError(HomeCraftException):
    """Exception raised for project-related failures."""
    
    def __init__(
        self,
        message: str = "Project operation failed",
        project_id: Optional[str] = None,
        operation: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            error_code="PROJECT_ERROR",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=details or {}
        )
        if project_id:
            self.details["project_id"] = project_id
        if operation:
            self.details["operation"] = operation


# Utility functions for exception handling

def create_http_exception(
    exception: HomeCraftException
) -> HTTPException:
    """Convert HomeCraft exception to FastAPI HTTPException."""
    return HTTPException(
        status_code=exception.status_code,
        detail={
            "error_code": exception.error_code,
            "message": exception.message,
            "details": exception.details
        }
    )


def handle_database_error(error: Exception) -> DatabaseError:
    """Convert database errors to HomeCraft DatabaseError."""
    error_message = str(error)
    
    # Map common database errors
    if "duplicate key" in error_message.lower():
        return ConflictError("Resource already exists")
    elif "foreign key" in error_message.lower():
        return ValidationError("Invalid reference to related resource")
    elif "not null" in error_message.lower():
        return ValidationError("Required field is missing")
    else:
        return DatabaseError(f"Database operation failed: {error_message}")


def handle_external_service_error(
    error: Exception,
    service_name: str,
    status_code: Optional[int] = None
) -> ExternalServiceError:
    """Convert external service errors to HomeCraft ExternalServiceError."""
    return ExternalServiceError(
        message=f"{service_name} service error: {str(error)}",
        service_name=service_name,
        service_status=status_code
    )
