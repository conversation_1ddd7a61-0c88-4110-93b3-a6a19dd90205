# HomeCraft Intelligence - Backend Environment Configuration
# Copy this file to .env and update the values for your environment

# Application Settings
APP_NAME="HomeCraft Intelligence"
ENVIRONMENT=development
DEBUG=true
VERSION=1.0.0

# Server Configuration
HOST=0.0.0.0
PORT=8000
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,https://homecraft-intelligence.com

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/homecraft_intelligence
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20
DATABASE_ECHO=false

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_CACHE_TTL=3600

# Security Configuration
SECRET_KEY=your-secret-key-change-in-production-make-it-very-long-and-random
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
BCRYPT_ROUNDS=12

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# AI/ML Configuration
EMBEDDING_MODEL=all-MiniLM-L6-v2
EMBEDDING_DIMENSION=384

# Chroma Vector Database
CHROMA_HOST=localhost
CHROMA_PORT=8001
CHROMA_COLLECTION_NAME=homecraft_knowledge

# LLM Configuration
LLM_PROVIDER=ollama
LLM_MODEL=llama2:7b
LLM_TEMPERATURE=0.7
LLM_MAX_TOKENS=2048

# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434

# OpenAI Configuration (Optional)
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-3.5-turbo

# Hugging Face Configuration (Optional)
HUGGINGFACE_API_KEY=your-huggingface-api-key-here
HUGGINGFACE_MODEL=microsoft/DialoGPT-medium

# External APIs
WEATHER_API_KEY=your-weather-api-key-here
WEATHER_API_URL=https://api.openweathermap.org/data/2.5

BUILDING_CODES_API_KEY=your-building-codes-api-key-here
BUILDING_CODES_API_URL=https://api.buildingcodes.com/v1

MATERIAL_PRICES_API_KEY=your-material-prices-api-key-here
MATERIAL_PRICES_API_URL=https://api.materialprices.com/v1

# File Storage
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp,application/pdf,text/plain

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_FILE=logs/homecraft.log

# Safety and Compliance
SAFETY_SCORE_THRESHOLD=0.7
REQUIRE_SAFETY_CHECK=true

# RAG Configuration
RAG_CHUNK_SIZE=1000
RAG_CHUNK_OVERLAP=200
RAG_TOP_K=5
RAG_SIMILARITY_THRESHOLD=0.7

# Content Moderation
ENABLE_CONTENT_MODERATION=true
MODERATION_THRESHOLD=0.8

# Monitoring and Analytics
ENABLE_METRICS=true
METRICS_PORT=9090

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_USE_TLS=true

# Webhooks
WEBHOOK_SECRET=your-webhook-secret-here

# Development Tools
ENABLE_DOCS=true
ENABLE_REDOC=true
ENABLE_OPENAPI=true
