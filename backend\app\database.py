"""
HomeCraft Intelligence - Database Configuration

This module handles database connection, session management, and initialization
for the HomeCraft Intelligence application using SQLAlchemy with PostgreSQL.
"""

import logging
from typing import AsyncGenerator
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import NullPool

from app.config import settings

# Create async engine
async_engine = create_async_engine(
    settings.DATABASE_URL,
    echo=settings.DATABASE_ECHO,
    pool_size=settings.DATABASE_POOL_SIZE,
    max_overflow=settings.DATABASE_MAX_OVERFLOW,
    poolclass=NullPool if settings.is_development else None,
)

# Create sync engine for Alembic migrations
sync_engine = create_engine(
    settings.database_url_sync,
    echo=settings.DATABASE_ECHO,
    pool_size=settings.DATABASE_POOL_SIZE,
    max_overflow=settings.DATABASE_MAX_OVERFLOW,
)

# Session makers
AsyncSessionLocal = async_sessionmaker(
    async_engine,
    class_=AsyncSession,
    expire_on_commit=False
)

SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=sync_engine
)

# Base class for models
Base = declarative_base()

# Metadata for migrations
metadata = MetaData()


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency to get database session.
    
    Yields:
        AsyncSession: Database session
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def init_db() -> None:
    """
    Initialize database tables.
    
    This function creates all tables defined in the models.
    In production, this should be handled by Alembic migrations.
    """
    try:
        # Import all models to ensure they are registered
        try:
            from app.models import user, project
            logging.info("Core models imported successfully")
        except ImportError as e:
            logging.warning(f"Some models could not be imported: {e}")

        if settings.ENVIRONMENT == "development":
            # In development, create tables directly
            async with async_engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            logging.info("Database tables created successfully")
        else:
            logging.info("Database initialization skipped in production (use Alembic)")
            
    except Exception as e:
        logging.error(f"Database initialization failed: {e}")
        raise


async def close_db() -> None:
    """Close database connections."""
    await async_engine.dispose()
    sync_engine.dispose()
    logging.info("Database connections closed")


class DatabaseManager:
    """Database manager for handling connections and transactions."""
    
    def __init__(self):
        self.async_engine = async_engine
        self.sync_engine = sync_engine
    
    async def create_session(self) -> AsyncSession:
        """Create a new async database session."""
        return AsyncSessionLocal()
    
    async def execute_query(self, query: str, params: dict = None):
        """Execute a raw SQL query."""
        async with AsyncSessionLocal() as session:
            result = await session.execute(query, params or {})
            await session.commit()
            return result
    
    async def health_check(self) -> bool:
        """Check database connectivity."""
        try:
            async with AsyncSessionLocal() as session:
                await session.execute("SELECT 1")
                return True
        except Exception as e:
            logging.error(f"Database health check failed: {e}")
            return False
    
    async def get_connection_info(self) -> dict:
        """Get database connection information."""
        return {
            "url": settings.DATABASE_URL.split("@")[-1],  # Hide credentials
            "pool_size": settings.DATABASE_POOL_SIZE,
            "max_overflow": settings.DATABASE_MAX_OVERFLOW,
            "echo": settings.DATABASE_ECHO
        }


# Global database manager instance
db_manager = DatabaseManager()


# Database utilities
class DatabaseUtils:
    """Utility functions for database operations."""
    
    @staticmethod
    async def table_exists(table_name: str) -> bool:
        """Check if a table exists in the database."""
        query = """
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = :table_name
        );
        """
        async with AsyncSessionLocal() as session:
            result = await session.execute(query, {"table_name": table_name})
            return result.scalar()
    
    @staticmethod
    async def get_table_count(table_name: str) -> int:
        """Get the number of rows in a table."""
        query = f"SELECT COUNT(*) FROM {table_name}"
        async with AsyncSessionLocal() as session:
            result = await session.execute(query)
            return result.scalar()
    
    @staticmethod
    async def truncate_table(table_name: str) -> None:
        """Truncate a table (remove all rows)."""
        query = f"TRUNCATE TABLE {table_name} RESTART IDENTITY CASCADE"
        async with AsyncSessionLocal() as session:
            await session.execute(query)
            await session.commit()
    
    @staticmethod
    async def backup_table(table_name: str, backup_name: str) -> None:
        """Create a backup of a table."""
        query = f"CREATE TABLE {backup_name} AS SELECT * FROM {table_name}"
        async with AsyncSessionLocal() as session:
            await session.execute(query)
            await session.commit()


# Database event listeners
from sqlalchemy import event
from sqlalchemy.engine import Engine
import time

if settings.is_development:
    @event.listens_for(Engine, "before_cursor_execute")
    def before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
        """Log SQL queries in development."""
        conn.info.setdefault('query_start_time', []).append(time.time())
        logging.debug(f"Start Query: {statement}")

    @event.listens_for(Engine, "after_cursor_execute")
    def after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
        """Log query execution time in development."""
        total = time.time() - conn.info['query_start_time'].pop(-1)
        logging.debug(f"Query Complete in {total:.4f}s")


# Connection pool monitoring
async def get_pool_status() -> dict:
    """Get connection pool status."""
    pool = async_engine.pool
    return {
        "size": pool.size(),
        "checked_in": pool.checkedin(),
        "checked_out": pool.checkedout(),
        "overflow": pool.overflow(),
        "invalid": pool.invalid()
    }
