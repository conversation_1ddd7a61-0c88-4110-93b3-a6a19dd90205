"""
HomeCraft Intelligence - Embedding Service

This module handles text embedding generation and vector similarity search
for the RAG pipeline using Sentence Transformers and Chroma vector database.

Author: HomeCraft Intelligence Team
License: MIT
"""

import logging
import asyncio
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
from sentence_transformers import SentenceTransformer
import chromadb
from chromadb.config import Settings
import redis.asyncio as redis
import json
import hashlib
from datetime import datetime, timedelta

from app.config import settings
from app.core.exceptions import EmbeddingError, VectorSearchError


class EmbeddingService:
    """
    Service for generating text embeddings and performing vector similarity search.
    
    This service provides:
    - Text embedding generation using Sentence Transformers
    - Vector storage and retrieval using Chroma
    - Caching for improved performance
    - Batch processing for efficiency
    """
    
    def __init__(self):
        self.model_name = settings.EMBEDDING_MODEL
        self.dimension = settings.EMBEDDING_DIMENSION
        self.collection_name = settings.CHROMA_COLLECTION_NAME
        self.model = None
        self.chroma_client = None
        self.collection = None
        self.redis_client = None
        self.cache_ttl = settings.REDIS_CACHE_TTL
        
    async def initialize(self) -> None:
        """Initialize the embedding service components."""
        try:
            # Initialize Sentence Transformer model
            logging.info(f"Loading embedding model: {self.model_name}")
            self.model = SentenceTransformer(self.model_name)
            
            # Initialize Chroma client
            self.chroma_client = chromadb.HttpClient(
                host=settings.CHROMA_HOST,
                port=settings.CHROMA_PORT,
                settings=Settings(
                    chroma_client_auth_provider="chromadb.auth.basic.BasicAuthClientProvider",
                    chroma_client_auth_credentials_provider="chromadb.auth.basic.BasicAuthCredentialsProvider"
                )
            )
            
            # Get or create collection
            try:
                self.collection = self.chroma_client.get_collection(
                    name=self.collection_name
                )
                logging.info(f"Connected to existing collection: {self.collection_name}")
            except Exception:
                self.collection = self.chroma_client.create_collection(
                    name=self.collection_name,
                    metadata={"description": "HomeCraft Intelligence knowledge base"}
                )
                logging.info(f"Created new collection: {self.collection_name}")
            
            # Initialize Redis for caching
            self.redis_client = redis.from_url(
                settings.REDIS_URL,
                encoding="utf-8",
                decode_responses=True
            )
            
            # Test connections
            await self._test_connections()
            
            logging.info("Embedding service initialized successfully")
            
        except Exception as e:
            logging.error(f"Failed to initialize embedding service: {e}")
            raise EmbeddingError(f"Initialization failed: {str(e)}")
    
    async def _test_connections(self) -> None:
        """Test all service connections."""
        try:
            # Test model
            test_embedding = self.model.encode(["test"])
            assert len(test_embedding[0]) == self.dimension
            
            # Test Chroma
            self.collection.count()
            
            # Test Redis
            await self.redis_client.ping()
            
        except Exception as e:
            raise EmbeddingError(f"Connection test failed: {str(e)}")
    
    async def embed_text(self, text: str, use_cache: bool = True) -> List[float]:
        """
        Generate embedding for a single text.
        
        Args:
            text: Input text to embed
            use_cache: Whether to use Redis cache
            
        Returns:
            List of embedding values
        """
        if not text or not text.strip():
            raise EmbeddingError("Empty text provided for embedding")
        
        try:
            # Check cache first
            if use_cache:
                cached_embedding = await self._get_cached_embedding(text)
                if cached_embedding:
                    return cached_embedding
            
            # Generate embedding
            embedding = await asyncio.get_event_loop().run_in_executor(
                None, self.model.encode, [text.strip()]
            )
            
            embedding_list = embedding[0].tolist()
            
            # Cache the result
            if use_cache:
                await self._cache_embedding(text, embedding_list)
            
            return embedding_list
            
        except Exception as e:
            logging.error(f"Embedding generation failed: {e}")
            raise EmbeddingError(f"Failed to generate embedding: {str(e)}")
    
    async def embed_batch(
        self, 
        texts: List[str], 
        batch_size: int = 32,
        use_cache: bool = True
    ) -> List[List[float]]:
        """
        Generate embeddings for multiple texts efficiently.
        
        Args:
            texts: List of texts to embed
            batch_size: Number of texts to process at once
            use_cache: Whether to use Redis cache
            
        Returns:
            List of embedding lists
        """
        if not texts:
            return []
        
        try:
            embeddings = []
            
            for i in range(0, len(texts), batch_size):
                batch = texts[i:i + batch_size]
                
                # Check cache for batch items
                batch_embeddings = []
                uncached_texts = []
                uncached_indices = []
                
                if use_cache:
                    for j, text in enumerate(batch):
                        cached = await self._get_cached_embedding(text)
                        if cached:
                            batch_embeddings.append(cached)
                        else:
                            batch_embeddings.append(None)
                            uncached_texts.append(text)
                            uncached_indices.append(j)
                else:
                    uncached_texts = batch
                    uncached_indices = list(range(len(batch)))
                    batch_embeddings = [None] * len(batch)
                
                # Generate embeddings for uncached texts
                if uncached_texts:
                    new_embeddings = await asyncio.get_event_loop().run_in_executor(
                        None, self.model.encode, uncached_texts
                    )
                    
                    # Insert new embeddings and cache them
                    for idx, embedding in zip(uncached_indices, new_embeddings):
                        embedding_list = embedding.tolist()
                        batch_embeddings[idx] = embedding_list
                        
                        if use_cache:
                            await self._cache_embedding(batch[idx], embedding_list)
                
                embeddings.extend(batch_embeddings)
            
            return embeddings
            
        except Exception as e:
            logging.error(f"Batch embedding generation failed: {e}")
            raise EmbeddingError(f"Failed to generate batch embeddings: {str(e)}")
    
    async def add_documents(
        self,
        documents: List[Dict[str, Any]],
        batch_size: int = 100
    ) -> None:
        """
        Add documents to the vector database.
        
        Args:
            documents: List of document dictionaries with 'content', 'metadata', and 'id'
            batch_size: Number of documents to process at once
        """
        try:
            for i in range(0, len(documents), batch_size):
                batch = documents[i:i + batch_size]
                
                # Extract content for embedding
                contents = [doc['content'] for doc in batch]
                
                # Generate embeddings
                embeddings = await self.embed_batch(contents, use_cache=False)
                
                # Prepare data for Chroma
                ids = [doc['id'] for doc in batch]
                metadatas = [doc.get('metadata', {}) for doc in batch]
                
                # Add to collection
                self.collection.add(
                    embeddings=embeddings,
                    documents=contents,
                    metadatas=metadatas,
                    ids=ids
                )
                
                logging.info(f"Added batch {i//batch_size + 1} ({len(batch)} documents)")
            
            logging.info(f"Successfully added {len(documents)} documents to vector database")
            
        except Exception as e:
            logging.error(f"Failed to add documents: {e}")
            raise VectorSearchError(f"Document addition failed: {str(e)}")
    
    async def search_similar(
        self,
        query_embedding: List[float],
        top_k: int = 5,
        threshold: float = 0.7,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for similar documents using vector similarity.
        
        Args:
            query_embedding: Query embedding vector
            top_k: Number of results to return
            threshold: Minimum similarity threshold
            filters: Metadata filters for search
            
        Returns:
            List of similar documents with scores
        """
        try:
            # Perform similarity search
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=top_k * 2,  # Get more results for filtering
                where=filters,
                include=['documents', 'metadatas', 'distances']
            )
            
            # Process results
            similar_docs = []
            
            if results['documents'] and results['documents'][0]:
                for i, (doc, metadata, distance) in enumerate(zip(
                    results['documents'][0],
                    results['metadatas'][0],
                    results['distances'][0]
                )):
                    # Convert distance to similarity score (Chroma uses L2 distance)
                    similarity = 1 / (1 + distance)
                    
                    if similarity >= threshold:
                        similar_docs.append({
                            'content': doc,
                            'metadata': metadata,
                            'score': similarity,
                            'rank': i + 1
                        })
            
            # Sort by similarity score and limit results
            similar_docs.sort(key=lambda x: x['score'], reverse=True)
            return similar_docs[:top_k]
            
        except Exception as e:
            logging.error(f"Vector search failed: {e}")
            raise VectorSearchError(f"Search failed: {str(e)}")
    
    async def search_by_text(
        self,
        query_text: str,
        top_k: int = 5,
        threshold: float = 0.7,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for similar documents using text query.
        
        Args:
            query_text: Text query to search for
            top_k: Number of results to return
            threshold: Minimum similarity threshold
            filters: Metadata filters for search
            
        Returns:
            List of similar documents with scores
        """
        try:
            # Generate query embedding
            query_embedding = await self.embed_text(query_text)
            
            # Perform search
            return await self.search_similar(
                query_embedding=query_embedding,
                top_k=top_k,
                threshold=threshold,
                filters=filters
            )
            
        except Exception as e:
            logging.error(f"Text search failed: {e}")
            raise VectorSearchError(f"Text search failed: {str(e)}")
    
    async def _get_cached_embedding(self, text: str) -> Optional[List[float]]:
        """Get embedding from Redis cache."""
        try:
            cache_key = self._get_cache_key(text)
            cached_data = await self.redis_client.get(cache_key)
            
            if cached_data:
                return json.loads(cached_data)
            
            return None
            
        except Exception as e:
            logging.warning(f"Cache retrieval failed: {e}")
            return None
    
    async def _cache_embedding(self, text: str, embedding: List[float]) -> None:
        """Cache embedding in Redis."""
        try:
            cache_key = self._get_cache_key(text)
            await self.redis_client.setex(
                cache_key,
                self.cache_ttl,
                json.dumps(embedding)
            )
            
        except Exception as e:
            logging.warning(f"Cache storage failed: {e}")
    
    def _get_cache_key(self, text: str) -> str:
        """Generate cache key for text."""
        text_hash = hashlib.md5(text.encode()).hexdigest()
        return f"embedding:{self.model_name}:{text_hash}"
    
    async def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics about the vector collection."""
        try:
            count = self.collection.count()
            
            return {
                'total_documents': count,
                'collection_name': self.collection_name,
                'embedding_model': self.model_name,
                'embedding_dimension': self.dimension,
                'last_updated': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logging.error(f"Failed to get collection stats: {e}")
            return {}
    
    async def cleanup(self) -> None:
        """Clean up resources."""
        try:
            if self.redis_client:
                await self.redis_client.close()
            
            logging.info("Embedding service cleanup completed")
            
        except Exception as e:
            logging.error(f"Cleanup failed: {e}")


# Global embedding service instance
embedding_service = EmbeddingService()
