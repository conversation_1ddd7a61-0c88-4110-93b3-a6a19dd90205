"""
HomeCraft Intelligence - Logging Configuration

This module sets up structured logging for the application with
proper formatting, levels, and output destinations.

Author: HomeCraft Intelligence Team
License: MIT
"""

import logging
import logging.config
import sys
from pathlib import Path
from typing import Dict, Any

from app.config import settings


def setup_logging() -> None:
    """
    Set up application logging configuration.
    
    Configures structured logging with appropriate levels,
    formatters, and handlers for development and production.
    """
    
    # Create logs directory if it doesn't exist
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Logging configuration
    logging_config: Dict[str, Any] = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S"
            },
            "detailed": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s:%(lineno)d - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S"
            },
            "json": {
                "()": "pythonjsonlogger.jsonlogger.JsonFormatter",
                "format": "%(asctime)s %(name)s %(levelname)s %(module)s %(funcName)s %(lineno)d %(message)s"
            }
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": "INFO",
                "formatter": "default",
                "stream": sys.stdout
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "DEBUG",
                "formatter": "detailed",
                "filename": "logs/homecraft.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf8"
            },
            "error_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "ERROR",
                "formatter": "detailed",
                "filename": "logs/errors.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf8"
            }
        },
        "loggers": {
            "app": {
                "level": "DEBUG",
                "handlers": ["console", "file", "error_file"],
                "propagate": False
            },
            "uvicorn": {
                "level": "INFO",
                "handlers": ["console", "file"],
                "propagate": False
            },
            "uvicorn.error": {
                "level": "INFO",
                "handlers": ["console", "file", "error_file"],
                "propagate": False
            },
            "uvicorn.access": {
                "level": "INFO",
                "handlers": ["console", "file"],
                "propagate": False
            },
            "sqlalchemy.engine": {
                "level": "WARNING",
                "handlers": ["console", "file"],
                "propagate": False
            }
        },
        "root": {
            "level": "INFO",
            "handlers": ["console", "file"]
        }
    }
    
    # Adjust logging level based on environment
    if settings.ENVIRONMENT == "development":
        logging_config["handlers"]["console"]["level"] = "DEBUG"
        logging_config["loggers"]["sqlalchemy.engine"]["level"] = "INFO"
    elif settings.ENVIRONMENT == "production":
        logging_config["handlers"]["console"]["level"] = "WARNING"
        logging_config["formatters"]["default"] = logging_config["formatters"]["json"]
    
    # Apply logging configuration
    logging.config.dictConfig(logging_config)
    
    # Set up root logger
    logger = logging.getLogger("app")
    logger.info(f"Logging configured for environment: {settings.ENVIRONMENT}")


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance with the specified name.
    
    Args:
        name: Logger name (typically __name__)
        
    Returns:
        Configured logger instance
    """
    return logging.getLogger(f"app.{name}")


# Convenience function for getting module loggers
def get_module_logger() -> logging.Logger:
    """Get logger for the calling module."""
    import inspect
    frame = inspect.currentframe()
    if frame and frame.f_back:
        module_name = frame.f_back.f_globals.get('__name__', 'unknown')
        return get_logger(module_name.replace('app.', ''))
    return get_logger('unknown')
