"""
HomeCraft Intelligence - Safety API Routes

This module defines the API endpoints for safety assessment,
building code compliance, and safety recommendations for DIY projects.

Author: HomeCraft Intelligence Team
License: MIT
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field
import logging

from app.database import get_db
from app.services.auth_service import auth_service
from app.services.safety_service import safety_service
from app.models.user import User
from app.core.exceptions import ValidationError, SafetyError, ComplianceError
from app.middleware.rate_limit import rate_limit


router = APIRouter()
security = HTTPBearer()


# Schemas
class SafetyAssessmentRequest(BaseModel):
    """Request schema for safety assessment."""
    project_description: str = Field(..., min_length=10, max_length=2000, description="Detailed project description")
    project_type: str = Field(..., description="Type of project (electrical, plumbing, etc.)")
    user_skill_level: str = Field(..., description="User's skill level")
    location: Optional[str] = Field(None, description="Location for local building codes")
    additional_context: Optional[Dict[str, Any]] = Field(None, description="Additional project context")


class SafetyWarningRequest(BaseModel):
    """Request schema for safety warnings."""
    query: str = Field(..., min_length=5, max_length=1000, description="User query or question")
    response: str = Field(..., min_length=10, max_length=2000, description="AI response to analyze")
    context: Optional[Dict[str, Any]] = Field(None, description="Additional context")


class SafetyAssessmentResponse(BaseModel):
    """Response schema for safety assessment."""
    safety_score: float = Field(..., ge=0, le=100, description="Overall safety score (0-100)")
    risk_level: str = Field(..., description="Risk level (low, medium, high, very_high)")
    hazards_identified: List[Dict[str, Any]] = Field(..., description="Identified hazards")
    safety_warnings: List[str] = Field(..., description="Safety warnings and precautions")
    code_requirements: Dict[str, Any] = Field(..., description="Building code requirements")
    recommendations: List[str] = Field(..., description="Safety recommendations")
    professional_required: bool = Field(..., description="Whether professional help is required")
    permit_required: bool = Field(..., description="Whether permits are required")
    assessment_timestamp: str = Field(..., description="Assessment timestamp")


class BuildingCodeResponse(BaseModel):
    """Response schema for building code information."""
    project_type: str
    location: Optional[str]
    permit_required: bool
    professional_required: bool
    inspection_required: bool
    applicable_codes: List[str]
    requirements: List[Dict[str, Any]]
    estimated_permit_cost: Optional[float]
    processing_time: Optional[str]


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """Dependency to get current authenticated user."""
    try:
        access_token = credentials.credentials
        return await auth_service.get_current_user(db, access_token)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )


@router.post("/assess", response_model=SafetyAssessmentResponse)
@rate_limit(requests_per_minute=15, requests_per_hour=150)
async def assess_project_safety(
    request: SafetyAssessmentRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Perform comprehensive safety assessment for a DIY project.
    
    This endpoint analyzes project descriptions and provides:
    - Safety score and risk level assessment
    - Hazard identification and warnings
    - Building code compliance requirements
    - Professional help recommendations
    - Safety precautions and best practices
    """
    try:
        # Validate skill level
        valid_skill_levels = ['beginner', 'intermediate', 'advanced', 'professional']
        if request.user_skill_level not in valid_skill_levels:
            raise ValidationError(f"Invalid skill level. Must be one of: {valid_skill_levels}")
        
        # Validate project type
        valid_project_types = ['electrical', 'plumbing', 'structural', 'roofing', 'hvac', 'flooring', 'painting']
        if request.project_type not in valid_project_types:
            raise ValidationError(f"Invalid project type. Must be one of: {valid_project_types}")
        
        # Perform safety assessment
        assessment = await safety_service.assess_project_safety(
            project_description=request.project_description,
            project_type=request.project_type,
            user_skill_level=request.user_skill_level,
            location=request.location
        )
        
        logging.info(f"Safety assessment completed for user {current_user.id}, score: {assessment['safety_score']}")
        
        return SafetyAssessmentResponse(**assessment)
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except SafetyError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logging.error(f"Safety assessment failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to perform safety assessment"
        )


@router.post("/warnings", response_model=List[str])
@rate_limit(requests_per_minute=30, requests_per_hour=200)
async def get_safety_warnings(
    request: SafetyWarningRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get safety warnings for specific queries and responses.
    
    Analyzes user queries and AI responses to identify potential
    safety concerns and provide appropriate warnings.
    """
    try:
        # Get safety warnings
        warnings = await safety_service.get_safety_warnings(
            query=request.query,
            response=request.response,
            context=request.context
        )
        
        logging.info(f"Safety warnings generated for user {current_user.id}")
        
        return warnings
        
    except Exception as e:
        logging.error(f"Safety warning generation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate safety warnings"
        )


@router.get("/codes/{project_type}", response_model=BuildingCodeResponse)
@rate_limit(requests_per_minute=20, requests_per_hour=100)
async def get_building_codes(
    project_type: str,
    location: Optional[str] = Query(None, description="Location for local building codes"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get building code requirements for a specific project type and location.
    
    Returns comprehensive information about:
    - Permit requirements
    - Professional licensing requirements
    - Inspection requirements
    - Applicable building codes
    - Estimated costs and timelines
    """
    try:
        # Validate project type
        valid_project_types = ['electrical', 'plumbing', 'structural', 'roofing', 'hvac']
        if project_type not in valid_project_types:
            raise ValidationError(f"Invalid project type. Must be one of: {valid_project_types}")
        
        # Get building code requirements
        code_requirements = await safety_service._check_building_codes(
            project_type=project_type,
            location=location
        )
        
        # Enhance with additional information
        enhanced_response = {
            "project_type": project_type,
            "location": location,
            "permit_required": code_requirements.get("permit_required", False),
            "professional_required": code_requirements.get("professional_required", False),
            "inspection_required": code_requirements.get("inspection_required", False),
            "applicable_codes": code_requirements.get("codes", []),
            "requirements": [
                {
                    "category": "Permits",
                    "description": "Building permits may be required for this type of work",
                    "mandatory": code_requirements.get("permit_required", False)
                },
                {
                    "category": "Professional Installation",
                    "description": "Licensed professional may be required",
                    "mandatory": code_requirements.get("professional_required", False)
                },
                {
                    "category": "Inspections",
                    "description": "Official inspections may be required",
                    "mandatory": code_requirements.get("inspection_required", False)
                }
            ],
            "estimated_permit_cost": 150.0 if code_requirements.get("permit_required") else None,
            "processing_time": "2-4 weeks" if code_requirements.get("permit_required") else None
        }
        
        logging.info(f"Building codes retrieved for {project_type} by user {current_user.id}")
        
        return BuildingCodeResponse(**enhanced_response)
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except ComplianceError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logging.error(f"Building codes retrieval failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve building codes"
        )


@router.get("/guidelines/{category}")
@rate_limit(requests_per_minute=25, requests_per_hour=150)
async def get_safety_guidelines(
    category: str,
    skill_level: Optional[str] = Query(None, description="User skill level for tailored guidelines"),
    current_user: User = Depends(get_current_user)
):
    """
    Get safety guidelines for specific categories and skill levels.
    
    Returns detailed safety guidelines, best practices, and
    precautions tailored to the user's skill level.
    """
    try:
        # Safety guidelines by category
        guidelines_db = {
            "electrical": {
                "general": [
                    "Always turn off power at the circuit breaker before starting work",
                    "Use a non-contact voltage tester to verify power is off",
                    "Never work on electrical systems in wet conditions",
                    "Wear rubber-soled shoes and avoid metal ladders"
                ],
                "beginner": [
                    "Consider hiring a licensed electrician for complex work",
                    "Start with simple tasks like replacing outlets or switches",
                    "Always have someone else present when working with electricity",
                    "Take a basic electrical safety course before starting"
                ],
                "advanced": [
                    "Understand local electrical codes and permit requirements",
                    "Use proper GFCI protection in wet locations",
                    "Follow proper wire sizing and circuit loading guidelines",
                    "Install proper grounding and bonding"
                ]
            },
            "plumbing": {
                "general": [
                    "Shut off water supply before starting work",
                    "Have towels and buckets ready for water spillage",
                    "Check local codes for pipe materials and installation methods",
                    "Test all connections for leaks before closing up walls"
                ],
                "beginner": [
                    "Start with simple repairs like replacing faucets",
                    "Use compression fittings for easier connections",
                    "Have a plumber's phone number ready for emergencies",
                    "Practice soldering on scrap pipes first"
                ],
                "advanced": [
                    "Understand proper pipe sizing and pressure requirements",
                    "Follow proper venting and drainage slope requirements",
                    "Use appropriate materials for different applications",
                    "Install proper backflow prevention devices"
                ]
            },
            "general": {
                "general": [
                    "Always wear appropriate personal protective equipment (PPE)",
                    "Read and follow all manufacturer instructions",
                    "Keep a well-stocked first aid kit nearby",
                    "Work in well-lit areas with proper ventilation",
                    "Take breaks to avoid fatigue-related accidents"
                ],
                "tool_safety": [
                    "Inspect tools before each use",
                    "Use the right tool for the job",
                    "Keep cutting tools sharp and clean",
                    "Store tools properly when not in use",
                    "Never bypass safety guards or features"
                ]
            }
        }
        
        # Get guidelines for category
        category_guidelines = guidelines_db.get(category, guidelines_db["general"])
        
        # Filter by skill level if provided
        guidelines = category_guidelines.get("general", [])
        if skill_level and skill_level in category_guidelines:
            guidelines.extend(category_guidelines[skill_level])
        
        response = {
            "category": category,
            "skill_level": skill_level,
            "guidelines": guidelines,
            "total_guidelines": len(guidelines),
            "last_updated": "2024-01-15T10:00:00Z"
        }
        
        logging.info(f"Safety guidelines retrieved for {category} by user {current_user.id}")
        
        return response
        
    except Exception as e:
        logging.error(f"Safety guidelines retrieval failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve safety guidelines"
        )


@router.get("/checklist/{project_type}")
@rate_limit(requests_per_minute=20, requests_per_hour=100)
async def get_safety_checklist(
    project_type: str,
    current_user: User = Depends(get_current_user)
):
    """
    Get a comprehensive safety checklist for a specific project type.
    
    Returns a structured checklist with pre-work, during-work,
    and post-work safety items to verify.
    """
    try:
        # Safety checklists by project type
        checklists = {
            "electrical": {
                "pre_work": [
                    "Turn off power at circuit breaker",
                    "Test circuits with voltage tester",
                    "Gather proper tools and PPE",
                    "Review electrical codes and permits",
                    "Plan wire routing and connections"
                ],
                "during_work": [
                    "Double-check power is off before touching wires",
                    "Use insulated tools",
                    "Make secure wire connections",
                    "Follow proper wire color coding",
                    "Install proper strain relief"
                ],
                "post_work": [
                    "Test all connections before energizing",
                    "Install proper cover plates",
                    "Test GFCI outlets if installed",
                    "Update electrical panel labels",
                    "Schedule inspection if required"
                ]
            },
            "plumbing": {
                "pre_work": [
                    "Shut off water supply",
                    "Drain pipes if necessary",
                    "Gather proper tools and materials",
                    "Check local plumbing codes",
                    "Plan pipe routing and connections"
                ],
                "during_work": [
                    "Make clean, square cuts on pipes",
                    "Use proper fittings and connections",
                    "Apply pipe dope or tape correctly",
                    "Support pipes properly",
                    "Maintain proper slope for drainage"
                ],
                "post_work": [
                    "Test all connections for leaks",
                    "Turn water supply back on slowly",
                    "Check water pressure and flow",
                    "Insulate pipes if in cold areas",
                    "Clean up and dispose of materials properly"
                ]
            }
        }
        
        checklist = checklists.get(project_type, {
            "pre_work": ["Plan the project thoroughly", "Gather necessary tools and materials"],
            "during_work": ["Work safely and follow best practices"],
            "post_work": ["Clean up and inspect completed work"]
        })
        
        response = {
            "project_type": project_type,
            "checklist": checklist,
            "total_items": sum(len(items) for items in checklist.values()),
            "estimated_time": "30-60 minutes for checklist completion"
        }
        
        logging.info(f"Safety checklist retrieved for {project_type} by user {current_user.id}")
        
        return response
        
    except Exception as e:
        logging.error(f"Safety checklist retrieval failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve safety checklist"
        )
