"""
HomeCraft Intelligence - Materials API Routes

This module defines the API endpoints for material calculations,
cost estimation, and material recommendations for DIY projects.

Author: HomeCraft Intelligence Team
License: MIT
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field
import logging

from app.database import get_db
from app.services.auth_service import auth_service
from app.services.material_service import material_service
from app.models.user import User
from app.core.exceptions import ValidationError, MaterialError
from app.middleware.rate_limit import rate_limit


router = APIRouter()
security = HTTPBearer()


# Schemas
class MaterialCalculationRequest(BaseModel):
    """Request schema for material calculations."""
    project_type: str = Field(..., description="Type of project (flooring, painting, etc.)")
    material_type: str = Field(..., description="Specific material type")
    dimensions: Dict[str, float] = Field(..., description="Project dimensions")
    custom_waste_factor: Optional[float] = Field(None, ge=0, le=1, description="Custom waste factor")
    location: Optional[str] = Field(None, description="Location for pricing")


class MaterialAlternativesRequest(BaseModel):
    """Request schema for material alternatives."""
    project_type: str = Field(..., description="Type of project")
    material_type: str = Field(..., description="Current material type")
    budget_min: Optional[float] = Field(None, ge=0, description="Minimum budget")
    budget_max: Optional[float] = Field(None, ge=0, description="Maximum budget")


class MaterialCalculationResponse(BaseModel):
    """Response schema for material calculations."""
    project_type: str
    material_type: str
    base_quantity: float
    waste_allowance: float
    total_quantity: float
    units_needed: int
    unit_type: str
    material_cost: float
    waste_cost: float
    additional_materials: List[Dict[str, Any]]
    tools_required: List[str]
    total_estimated_cost: float
    pricing_info: Dict[str, Any]
    calculation_timestamp: str


class MaterialAlternativeResponse(BaseModel):
    """Response schema for material alternatives."""
    material_type: str
    average_cost: float
    cost_range: List[float]
    unit: str
    waste_factor: float
    tools_required: List[str]
    pros_cons: Dict[str, List[str]]


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """Dependency to get current authenticated user."""
    try:
        access_token = credentials.credentials
        return await auth_service.get_current_user(db, access_token)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )


@router.post("/calculate", response_model=MaterialCalculationResponse)
@rate_limit(requests_per_minute=20, requests_per_hour=200)
async def calculate_materials(
    request: MaterialCalculationRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Calculate material requirements and costs for a project.
    
    This endpoint provides comprehensive material calculations including:
    - Quantity calculations with waste factors
    - Cost estimates with current pricing
    - Additional materials and tools needed
    - Alternative material suggestions
    """
    try:
        # Validate project and material types
        valid_project_types = ['flooring', 'painting', 'drywall', 'electrical', 'plumbing']
        if request.project_type not in valid_project_types:
            raise ValidationError(f"Invalid project type. Must be one of: {valid_project_types}")
        
        # Calculate materials
        calculation = await material_service.calculate_materials(
            project_type=request.project_type,
            material_type=request.material_type,
            dimensions=request.dimensions,
            custom_waste_factor=request.custom_waste_factor
        )
        
        logging.info(f"Material calculation completed for user {current_user.id}")
        
        return MaterialCalculationResponse(**calculation)
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except MaterialError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logging.error(f"Material calculation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to calculate materials"
        )


@router.post("/alternatives", response_model=List[MaterialAlternativeResponse])
@rate_limit(requests_per_minute=15, requests_per_hour=100)
async def get_material_alternatives(
    request: MaterialAlternativesRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get alternative materials for a project within budget constraints.
    
    Returns a list of alternative materials with cost comparisons,
    pros/cons analysis, and compatibility information.
    """
    try:
        # Prepare budget range
        budget_range = None
        if request.budget_min is not None and request.budget_max is not None:
            budget_range = (request.budget_min, request.budget_max)
        
        # Get alternatives
        alternatives = await material_service.get_material_alternatives(
            project_type=request.project_type,
            material_type=request.material_type,
            budget_range=budget_range
        )
        
        logging.info(f"Material alternatives retrieved for user {current_user.id}")
        
        return [MaterialAlternativeResponse(**alt) for alt in alternatives]
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except MaterialError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logging.error(f"Material alternatives retrieval failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve material alternatives"
        )


@router.get("/categories")
@rate_limit(requests_per_minute=30, requests_per_hour=200)
async def get_material_categories(
    current_user: User = Depends(get_current_user)
):
    """
    Get available material categories and types.
    
    Returns a structured list of all supported project types
    and their associated material categories.
    """
    try:
        categories = {
            "flooring": {
                "materials": ["hardwood", "laminate", "tile", "carpet", "vinyl"],
                "description": "Floor covering materials and installation supplies"
            },
            "painting": {
                "materials": ["interior_paint", "exterior_paint", "primer", "stain"],
                "description": "Paint and coating materials"
            },
            "drywall": {
                "materials": ["drywall_sheet", "joint_compound", "drywall_tape"],
                "description": "Drywall installation and finishing materials"
            },
            "electrical": {
                "materials": ["wire_12awg", "wire_14awg", "outlet", "switch", "breaker"],
                "description": "Electrical components and wiring materials"
            },
            "plumbing": {
                "materials": ["pipe_pvc", "pipe_copper", "fittings", "fixtures"],
                "description": "Plumbing pipes, fittings, and fixtures"
            }
        }
        
        return {
            "categories": categories,
            "total_categories": len(categories),
            "total_materials": sum(len(cat["materials"]) for cat in categories.values())
        }
        
    except Exception as e:
        logging.error(f"Material categories retrieval failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve material categories"
        )


@router.get("/pricing/{material_type}")
@rate_limit(requests_per_minute=25, requests_per_hour=150)
async def get_material_pricing(
    material_type: str,
    location: Optional[str] = Query(None, description="Location for regional pricing"),
    current_user: User = Depends(get_current_user)
):
    """
    Get current pricing information for a specific material.
    
    Returns pricing data including average costs, price ranges,
    and regional variations if location is provided.
    """
    try:
        # This would integrate with the material service pricing lookup
        pricing_info = {
            "material_type": material_type,
            "average_price": 25.0,  # Placeholder
            "price_range": {"min": 15.0, "max": 35.0},
            "unit": "sq_ft",
            "currency": "USD",
            "location": location or "US Average",
            "last_updated": "2024-01-15T10:00:00Z",
            "source": "market_data",
            "confidence": 0.85
        }
        
        logging.info(f"Material pricing retrieved for {material_type} by user {current_user.id}")
        
        return pricing_info
        
    except Exception as e:
        logging.error(f"Material pricing retrieval failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve material pricing"
        )


@router.get("/tools/{project_type}")
@rate_limit(requests_per_minute=20, requests_per_hour=100)
async def get_required_tools(
    project_type: str,
    material_type: Optional[str] = Query(None, description="Specific material type"),
    current_user: User = Depends(get_current_user)
):
    """
    Get list of tools required for a specific project type and material.
    
    Returns comprehensive tool requirements including essential tools,
    optional tools, and rental vs purchase recommendations.
    """
    try:
        # Tool requirements by project type
        tool_requirements = {
            "flooring": {
                "essential": ["measuring_tape", "saw", "hammer", "spacers"],
                "optional": ["nail_gun", "miter_saw", "knee_pads"],
                "rental_recommended": ["floor_nailer", "tile_saw"]
            },
            "painting": {
                "essential": ["brushes", "rollers", "paint_tray", "drop_cloths"],
                "optional": ["sprayer", "extension_pole", "paint_mixer"],
                "rental_recommended": ["airless_sprayer", "scaffolding"]
            },
            "electrical": {
                "essential": ["wire_strippers", "voltage_tester", "screwdrivers"],
                "optional": ["multimeter", "fish_tape", "headlamp"],
                "rental_recommended": ["conduit_bender", "cable_puller"]
            }
        }
        
        tools = tool_requirements.get(project_type, {
            "essential": [],
            "optional": [],
            "rental_recommended": []
        })
        
        return {
            "project_type": project_type,
            "material_type": material_type,
            "tools": tools,
            "total_tools": sum(len(category) for category in tools.values()),
            "estimated_cost": {
                "purchase": 150.0,  # Placeholder
                "rental": 50.0
            }
        }
        
    except Exception as e:
        logging.error(f"Tool requirements retrieval failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve tool requirements"
        )
