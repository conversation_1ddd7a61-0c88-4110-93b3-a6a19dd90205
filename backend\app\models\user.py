"""
HomeCraft Intelligence - User Models

This module defines the User model and related database tables
for user management, authentication, and profile information.
"""

import uuid
from datetime import datetime
from typing import Optional, List
from sqlalchemy import (
    Column, String, Boolean, DateTime, Text, Integer, 
    Float, JSON, Foreign<PERSON>ey, Enum as SQLEnum
)
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum

from app.database import Base


class SkillLevel(str, enum.Enum):
    """User skill levels for DIY projects."""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    PROFESSIONAL = "professional"


class UserStatus(str, enum.Enum):
    """User account status."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PENDING_VERIFICATION = "pending_verification"


class User(Base):
    """
    User model for authentication and profile management.
    
    This model stores user account information, preferences,
    and skill assessments for personalized recommendations.
    """
    __tablename__ = "users"

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Authentication fields
    email = Column(String(255), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    status = Column(SQLEnum(UserStatus), default=UserStatus.PENDING_VERIFICATION, nullable=False)
    
    # Profile information
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)
    username = Column(String(50), unique=True, index=True, nullable=True)
    bio = Column(Text, nullable=True)
    avatar_url = Column(String(500), nullable=True)
    
    # Location and preferences
    location = Column(String(200), nullable=True)  # City, State, Country
    timezone = Column(String(50), default="UTC", nullable=False)
    language = Column(String(10), default="en", nullable=False)
    
    # Skill assessment
    skill_level = Column(SQLEnum(SkillLevel), default=SkillLevel.BEGINNER, nullable=False)
    skill_assessment_score = Column(Float, nullable=True)
    skill_assessment_date = Column(DateTime(timezone=True), nullable=True)
    specialties = Column(ARRAY(String), default=[], nullable=False)  # e.g., ["plumbing", "electrical"]
    
    # Safety preferences
    safety_preference_level = Column(Integer, default=5, nullable=False)  # 1-10 scale
    requires_safety_reminders = Column(Boolean, default=True, nullable=False)
    
    # Notification preferences
    email_notifications = Column(Boolean, default=True, nullable=False)
    push_notifications = Column(Boolean, default=True, nullable=False)
    community_notifications = Column(Boolean, default=True, nullable=False)
    
    # Subscription and premium features
    is_premium = Column(Boolean, default=False, nullable=False)
    premium_expires_at = Column(DateTime(timezone=True), nullable=True)
    subscription_type = Column(String(50), nullable=True)  # "basic", "premium", "pro"
    
    # Usage statistics
    total_projects = Column(Integer, default=0, nullable=False)
    completed_projects = Column(Integer, default=0, nullable=False)
    total_queries = Column(Integer, default=0, nullable=False)
    last_active_at = Column(DateTime(timezone=True), nullable=True)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    deleted_at = Column(DateTime(timezone=True), nullable=True)  # Soft delete
    
    # Additional profile data (JSON field for flexibility)
    profile_data = Column(JSON, default={}, nullable=False)
    
    # Relationships
    projects = relationship("Project", back_populates="owner", cascade="all, delete-orphan")
    queries = relationship("RAGQuery", back_populates="user", cascade="all, delete-orphan")
    community_posts = relationship("CommunityPost", back_populates="author", cascade="all, delete-orphan")
    user_sessions = relationship("UserSession", back_populates="user", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        return f"<User(id={self.id}, email={self.email}, skill_level={self.skill_level})>"
    
    @property
    def full_name(self) -> str:
        """Get user's full name."""
        return f"{self.first_name} {self.last_name}".strip()
    
    @property
    def is_premium_active(self) -> bool:
        """Check if premium subscription is active."""
        if not self.is_premium:
            return False
        if self.premium_expires_at is None:
            return True
        return datetime.utcnow() < self.premium_expires_at
    
    @property
    def completion_rate(self) -> float:
        """Calculate project completion rate."""
        if self.total_projects == 0:
            return 0.0
        return (self.completed_projects / self.total_projects) * 100
    
    def update_last_active(self) -> None:
        """Update last active timestamp."""
        self.last_active_at = datetime.utcnow()
    
    def increment_project_count(self) -> None:
        """Increment total project count."""
        self.total_projects += 1
    
    def increment_completed_projects(self) -> None:
        """Increment completed project count."""
        self.completed_projects += 1
    
    def increment_query_count(self) -> None:
        """Increment total query count."""
        self.total_queries += 1


class UserSession(Base):
    """
    User session model for tracking active sessions and JWT tokens.
    
    This model helps with session management, token revocation,
    and security monitoring.
    """
    __tablename__ = "user_sessions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    
    # Session information
    session_token = Column(String(500), unique=True, index=True, nullable=False)
    refresh_token = Column(String(500), unique=True, index=True, nullable=False)
    device_info = Column(JSON, default={}, nullable=False)  # Browser, OS, etc.
    ip_address = Column(String(45), nullable=True)  # IPv4 or IPv6
    user_agent = Column(Text, nullable=True)
    
    # Session status
    is_active = Column(Boolean, default=True, nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    last_used_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    revoked_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="user_sessions")
    
    def __repr__(self) -> str:
        return f"<UserSession(id={self.id}, user_id={self.user_id}, active={self.is_active})>"
    
    @property
    def is_expired(self) -> bool:
        """Check if session is expired."""
        return datetime.utcnow() > self.expires_at
    
    def revoke(self) -> None:
        """Revoke the session."""
        self.is_active = False
        self.revoked_at = datetime.utcnow()
    
    def update_last_used(self) -> None:
        """Update last used timestamp."""
        self.last_used_at = datetime.utcnow()


class UserPreferences(Base):
    """
    User preferences model for storing detailed user settings.
    
    This model stores user-specific preferences that don't fit
    in the main User model, providing flexibility for future features.
    """
    __tablename__ = "user_preferences"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    
    # Preference categories
    category = Column(String(50), nullable=False, index=True)  # "ui", "notifications", "safety", etc.
    key = Column(String(100), nullable=False)
    value = Column(JSON, nullable=False)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    user = relationship("User")
    
    def __repr__(self) -> str:
        return f"<UserPreferences(user_id={self.user_id}, category={self.category}, key={self.key})>"
