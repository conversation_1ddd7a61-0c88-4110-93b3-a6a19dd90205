"""
HomeCraft Intelligence - Material Service

This module handles material calculations, cost estimation, and material
recommendations for DIY home improvement projects.

Author: HomeCraft Intelligence Team
License: MIT
"""

import logging
import math
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import aiohttp
import json

from app.config import settings
from app.core.exceptions import MaterialError, CalculationError


class MaterialService:
    """
    Service for material calculations and cost estimation.
    
    This service provides:
    - Material quantity calculations for various project types
    - Cost estimation with current market prices
    - Material recommendations and alternatives
    - Waste factor calculations
    - Tool requirements identification
    """
    
    def __init__(self):
        self.session = None
        self.price_cache = {}
        self.cache_expiry = timedelta(hours=24)
        
        # Standard material specifications and calculations
        self.material_specs = {
            'flooring': {
                'hardwood': {
                    'unit': 'sq_ft',
                    'waste_factor': 0.10,
                    'coverage_per_unit': 1.0,
                    'typical_cost_range': (5.0, 15.0),
                    'tools_required': ['miter_saw', 'nail_gun', 'measuring_tape', 'spacers']
                },
                'laminate': {
                    'unit': 'sq_ft',
                    'waste_factor': 0.08,
                    'coverage_per_unit': 1.0,
                    'typical_cost_range': (2.0, 8.0),
                    'tools_required': ['circular_saw', 'tapping_block', 'spacers', 'underlayment']
                },
                'tile': {
                    'unit': 'sq_ft',
                    'waste_factor': 0.15,
                    'coverage_per_unit': 1.0,
                    'typical_cost_range': (3.0, 20.0),
                    'tools_required': ['tile_saw', 'trowel', 'spacers', 'level', 'grout_float']
                },
                'carpet': {
                    'unit': 'sq_ft',
                    'waste_factor': 0.10,
                    'coverage_per_unit': 1.0,
                    'typical_cost_range': (2.0, 12.0),
                    'tools_required': ['carpet_knife', 'knee_kicker', 'power_stretcher', 'tucker']
                }
            },
            'painting': {
                'interior_paint': {
                    'unit': 'gallon',
                    'coverage_per_unit': 350.0,  # sq ft per gallon
                    'waste_factor': 0.05,
                    'typical_cost_range': (25.0, 80.0),
                    'tools_required': ['brushes', 'rollers', 'paint_tray', 'drop_cloths', 'ladder']
                },
                'exterior_paint': {
                    'unit': 'gallon',
                    'coverage_per_unit': 300.0,  # sq ft per gallon
                    'waste_factor': 0.10,
                    'typical_cost_range': (30.0, 100.0),
                    'tools_required': ['brushes', 'rollers', 'sprayer', 'pressure_washer', 'ladder']
                },
                'primer': {
                    'unit': 'gallon',
                    'coverage_per_unit': 300.0,  # sq ft per gallon
                    'waste_factor': 0.05,
                    'typical_cost_range': (20.0, 60.0),
                    'tools_required': ['brushes', 'rollers', 'paint_tray']
                }
            },
            'drywall': {
                'drywall_sheet': {
                    'unit': 'sheet',
                    'coverage_per_unit': 32.0,  # 4x8 sheet = 32 sq ft
                    'waste_factor': 0.15,
                    'typical_cost_range': (12.0, 25.0),
                    'tools_required': ['drywall_saw', 'screw_gun', 'joint_knife', 'sanding_block']
                },
                'joint_compound': {
                    'unit': 'gallon',
                    'coverage_per_unit': 100.0,  # sq ft per gallon
                    'waste_factor': 0.10,
                    'typical_cost_range': (15.0, 30.0),
                    'tools_required': ['joint_knife', 'hawk', 'sanding_block']
                },
                'drywall_tape': {
                    'unit': 'roll',
                    'coverage_per_unit': 500.0,  # linear feet per roll
                    'waste_factor': 0.10,
                    'typical_cost_range': (3.0, 8.0),
                    'tools_required': ['joint_knife']
                }
            },
            'electrical': {
                'wire_12awg': {
                    'unit': 'foot',
                    'coverage_per_unit': 1.0,
                    'waste_factor': 0.20,
                    'typical_cost_range': (0.50, 1.50),
                    'tools_required': ['wire_strippers', 'electrical_pliers', 'voltage_tester']
                },
                'outlet': {
                    'unit': 'each',
                    'coverage_per_unit': 1.0,
                    'waste_factor': 0.05,
                    'typical_cost_range': (2.0, 15.0),
                    'tools_required': ['screwdriver', 'wire_strippers', 'voltage_tester']
                },
                'switch': {
                    'unit': 'each',
                    'coverage_per_unit': 1.0,
                    'waste_factor': 0.05,
                    'typical_cost_range': (3.0, 25.0),
                    'tools_required': ['screwdriver', 'wire_strippers', 'voltage_tester']
                }
            }
        }
    
    async def initialize(self) -> None:
        """Initialize the material service."""
        try:
            # Create HTTP session for price API calls
            timeout = aiohttp.ClientTimeout(total=30, connect=10)
            self.session = aiohttp.ClientSession(timeout=timeout)
            
            logging.info("Material service initialized successfully")
            
        except Exception as e:
            logging.error(f"Failed to initialize material service: {e}")
            raise MaterialError(f"Initialization failed: {str(e)}")
    
    async def calculate_materials(
        self,
        project_type: str,
        material_type: str,
        dimensions: Dict[str, float],
        custom_waste_factor: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        Calculate material requirements for a project.
        
        Args:
            project_type: Type of project (flooring, painting, etc.)
            material_type: Specific material (hardwood, paint, etc.)
            dimensions: Project dimensions (length, width, height, etc.)
            custom_waste_factor: Custom waste factor override
            
        Returns:
            Material calculation results with quantities and costs
        """
        try:
            # Get material specifications
            if project_type not in self.material_specs:
                raise MaterialError(f"Unsupported project type: {project_type}")
            
            if material_type not in self.material_specs[project_type]:
                raise MaterialError(f"Unsupported material type: {material_type}")
            
            specs = self.material_specs[project_type][material_type]
            
            # Calculate base area/quantity needed
            base_quantity = await self._calculate_base_quantity(
                project_type, dimensions, specs
            )
            
            # Apply waste factor
            waste_factor = custom_waste_factor or specs['waste_factor']
            total_quantity = base_quantity * (1 + waste_factor)
            
            # Calculate material units needed
            units_needed = math.ceil(total_quantity / specs['coverage_per_unit'])
            
            # Get current pricing
            pricing = await self._get_material_pricing(material_type, specs)
            
            # Calculate costs
            material_cost = units_needed * pricing['unit_price']
            waste_cost = (total_quantity - base_quantity) * pricing['unit_price'] / specs['coverage_per_unit']
            
            # Get additional materials and tools
            additional_materials = await self._get_additional_materials(
                project_type, material_type, base_quantity
            )
            
            tools_needed = specs.get('tools_required', [])
            
            return {
                'project_type': project_type,
                'material_type': material_type,
                'base_quantity': round(base_quantity, 2),
                'waste_allowance': round(total_quantity - base_quantity, 2),
                'total_quantity': round(total_quantity, 2),
                'units_needed': units_needed,
                'unit_type': specs['unit'],
                'material_cost': round(material_cost, 2),
                'waste_cost': round(waste_cost, 2),
                'additional_materials': additional_materials,
                'tools_required': tools_needed,
                'total_estimated_cost': round(
                    material_cost + sum(item['cost'] for item in additional_materials), 2
                ),
                'pricing_info': pricing,
                'calculation_timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logging.error(f"Material calculation failed: {e}")
            raise CalculationError(f"Material calculation failed: {str(e)}")
    
    async def _calculate_base_quantity(
        self,
        project_type: str,
        dimensions: Dict[str, float],
        specs: Dict[str, Any]
    ) -> float:
        """Calculate base quantity needed based on project type and dimensions."""
        try:
            if project_type in ['flooring', 'painting']:
                # Area-based calculations
                if 'area' in dimensions:
                    return dimensions['area']
                elif 'length' in dimensions and 'width' in dimensions:
                    return dimensions['length'] * dimensions['width']
                else:
                    raise CalculationError("Missing area or length/width dimensions")
            
            elif project_type == 'drywall':
                # Wall area calculation
                if 'walls' in dimensions:
                    total_area = 0
                    for wall in dimensions['walls']:
                        wall_area = wall.get('length', 0) * wall.get('height', 0)
                        # Subtract openings (doors, windows)
                        openings = wall.get('openings', [])
                        for opening in openings:
                            opening_area = opening.get('width', 0) * opening.get('height', 0)
                            wall_area -= opening_area
                        total_area += wall_area
                    return total_area
                else:
                    raise CalculationError("Missing wall dimensions for drywall calculation")
            
            elif project_type == 'electrical':
                # Count-based calculations
                if 'count' in dimensions:
                    return dimensions['count']
                elif 'linear_feet' in dimensions:
                    return dimensions['linear_feet']
                else:
                    raise CalculationError("Missing count or linear feet for electrical calculation")
            
            else:
                raise CalculationError(f"Unsupported project type for calculation: {project_type}")
                
        except Exception as e:
            raise CalculationError(f"Base quantity calculation failed: {str(e)}")
    
    async def _get_material_pricing(
        self,
        material_type: str,
        specs: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Get current material pricing from cache or external API."""
        try:
            # Check cache first
            cache_key = f"price_{material_type}"
            if cache_key in self.price_cache:
                cached_data = self.price_cache[cache_key]
                if datetime.utcnow() - cached_data['timestamp'] < self.cache_expiry:
                    return cached_data['pricing']
            
            # Try to fetch from external API
            if settings.MATERIAL_PRICES_API_KEY and settings.MATERIAL_PRICES_API_URL:
                api_pricing = await self._fetch_api_pricing(material_type)
                if api_pricing:
                    # Cache the result
                    self.price_cache[cache_key] = {
                        'pricing': api_pricing,
                        'timestamp': datetime.utcnow()
                    }
                    return api_pricing
            
            # Fall back to typical cost range
            cost_range = specs.get('typical_cost_range', (10.0, 50.0))
            estimated_price = (cost_range[0] + cost_range[1]) / 2
            
            pricing = {
                'unit_price': estimated_price,
                'price_range': {
                    'min': cost_range[0],
                    'max': cost_range[1]
                },
                'source': 'estimated',
                'currency': 'USD',
                'last_updated': datetime.utcnow().isoformat()
            }
            
            # Cache the estimated pricing
            self.price_cache[cache_key] = {
                'pricing': pricing,
                'timestamp': datetime.utcnow()
            }
            
            return pricing
            
        except Exception as e:
            logging.warning(f"Pricing lookup failed: {e}")
            # Return default pricing
            return {
                'unit_price': 25.0,
                'price_range': {'min': 10.0, 'max': 50.0},
                'source': 'default',
                'currency': 'USD',
                'last_updated': datetime.utcnow().isoformat()
            }
    
    async def _fetch_api_pricing(self, material_type: str) -> Optional[Dict[str, Any]]:
        """Fetch pricing from external API."""
        try:
            headers = {
                "Authorization": f"Bearer {settings.MATERIAL_PRICES_API_KEY}",
                "Content-Type": "application/json"
            }
            
            params = {
                "material": material_type,
                "location": "US",  # Default to US pricing
                "currency": "USD"
            }
            
            url = f"{settings.MATERIAL_PRICES_API_URL}/prices"
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return {
                        'unit_price': data.get('average_price', 25.0),
                        'price_range': {
                            'min': data.get('min_price', 10.0),
                            'max': data.get('max_price', 50.0)
                        },
                        'source': 'api',
                        'currency': data.get('currency', 'USD'),
                        'last_updated': data.get('last_updated', datetime.utcnow().isoformat())
                    }
                else:
                    logging.warning(f"Material pricing API returned {response.status}")
                    return None
                    
        except Exception as e:
            logging.warning(f"Failed to fetch API pricing: {e}")
            return None
    
    async def _get_additional_materials(
        self,
        project_type: str,
        material_type: str,
        base_quantity: float
    ) -> List[Dict[str, Any]]:
        """Get additional materials needed for the project."""
        additional_materials = []
        
        try:
            if project_type == 'flooring':
                if material_type in ['hardwood', 'laminate']:
                    additional_materials.extend([
                        {
                            'name': 'Underlayment',
                            'quantity': math.ceil(base_quantity / 100) * 100,  # Round up to 100 sq ft
                            'unit': 'sq_ft',
                            'estimated_cost': 0.50,
                            'cost': math.ceil(base_quantity / 100) * 50.0
                        },
                        {
                            'name': 'Transition strips',
                            'quantity': 2,
                            'unit': 'each',
                            'estimated_cost': 15.0,
                            'cost': 30.0
                        },
                        {
                            'name': 'Quarter round molding',
                            'quantity': math.ceil(base_quantity / 10),  # Estimate based on perimeter
                            'unit': 'linear_ft',
                            'estimated_cost': 2.0,
                            'cost': math.ceil(base_quantity / 10) * 2.0
                        }
                    ])
                
                elif material_type == 'tile':
                    additional_materials.extend([
                        {
                            'name': 'Tile adhesive',
                            'quantity': math.ceil(base_quantity / 50),  # 50 sq ft per bag
                            'unit': 'bag',
                            'estimated_cost': 25.0,
                            'cost': math.ceil(base_quantity / 50) * 25.0
                        },
                        {
                            'name': 'Grout',
                            'quantity': math.ceil(base_quantity / 100),  # 100 sq ft per bag
                            'unit': 'bag',
                            'estimated_cost': 15.0,
                            'cost': math.ceil(base_quantity / 100) * 15.0
                        },
                        {
                            'name': 'Tile spacers',
                            'quantity': 1,
                            'unit': 'bag',
                            'estimated_cost': 10.0,
                            'cost': 10.0
                        }
                    ])
            
            elif project_type == 'painting':
                additional_materials.extend([
                    {
                        'name': 'Painter\'s tape',
                        'quantity': 2,
                        'unit': 'roll',
                        'estimated_cost': 8.0,
                        'cost': 16.0
                    },
                    {
                        'name': 'Drop cloths',
                        'quantity': 2,
                        'unit': 'each',
                        'estimated_cost': 12.0,
                        'cost': 24.0
                    },
                    {
                        'name': 'Sandpaper',
                        'quantity': 1,
                        'unit': 'pack',
                        'estimated_cost': 15.0,
                        'cost': 15.0
                    }
                ])
            
            elif project_type == 'drywall':
                additional_materials.extend([
                    {
                        'name': 'Drywall screws',
                        'quantity': math.ceil(base_quantity / 32) * 2,  # 2 lbs per sheet
                        'unit': 'lb',
                        'estimated_cost': 8.0,
                        'cost': math.ceil(base_quantity / 32) * 2 * 8.0
                    },
                    {
                        'name': 'Corner bead',
                        'quantity': 4,  # Estimate 4 pieces for typical room
                        'unit': 'each',
                        'estimated_cost': 3.0,
                        'cost': 12.0
                    }
                ])
            
            return additional_materials
            
        except Exception as e:
            logging.warning(f"Failed to calculate additional materials: {e}")
            return []
    
    async def get_material_alternatives(
        self,
        project_type: str,
        material_type: str,
        budget_range: Optional[Tuple[float, float]] = None
    ) -> List[Dict[str, Any]]:
        """Get alternative materials for a project within budget range."""
        try:
            alternatives = []
            
            if project_type in self.material_specs:
                for alt_material, specs in self.material_specs[project_type].items():
                    if alt_material != material_type:
                        cost_range = specs.get('typical_cost_range', (0, 0))
                        avg_cost = (cost_range[0] + cost_range[1]) / 2
                        
                        # Filter by budget if provided
                        if budget_range:
                            if not (budget_range[0] <= avg_cost <= budget_range[1]):
                                continue
                        
                        alternatives.append({
                            'material_type': alt_material,
                            'average_cost': avg_cost,
                            'cost_range': cost_range,
                            'unit': specs['unit'],
                            'waste_factor': specs['waste_factor'],
                            'tools_required': specs.get('tools_required', []),
                            'pros_cons': await self._get_material_pros_cons(alt_material)
                        })
            
            # Sort by cost
            alternatives.sort(key=lambda x: x['average_cost'])
            
            return alternatives
            
        except Exception as e:
            logging.error(f"Failed to get material alternatives: {e}")
            return []
    
    async def _get_material_pros_cons(self, material_type: str) -> Dict[str, List[str]]:
        """Get pros and cons for a material type."""
        # This could be enhanced with a database or external service
        material_info = {
            'hardwood': {
                'pros': ['Durable', 'Beautiful natural appearance', 'Increases home value', 'Can be refinished'],
                'cons': ['Expensive', 'Susceptible to moisture', 'Requires maintenance', 'Installation complexity']
            },
            'laminate': {
                'pros': ['Affordable', 'Easy installation', 'Moisture resistant', 'Low maintenance'],
                'cons': ['Cannot be refinished', 'Less durable than hardwood', 'Can look artificial']
            },
            'tile': {
                'pros': ['Very durable', 'Water resistant', 'Easy to clean', 'Many design options'],
                'cons': ['Cold underfoot', 'Hard surface', 'Grout maintenance', 'Installation complexity']
            }
        }
        
        return material_info.get(material_type, {'pros': [], 'cons': []})
    
    async def cleanup(self) -> None:
        """Clean up resources."""
        try:
            if self.session:
                await self.session.close()
            
            logging.info("Material service cleanup completed")
            
        except Exception as e:
            logging.error(f"Material service cleanup failed: {e}")


# Global material service instance
material_service = MaterialService()
