# Contributing to HomeCraft Intelligence

Thank you for your interest in contributing to HomeCraft Intelligence! This document provides guidelines and information for contributors.

## 🎯 Project Vision

HomeCraft Intelligence aims to democratize home improvement knowledge by providing AI-powered, safety-first guidance to DIY enthusiasts of all skill levels. We believe everyone should have access to expert-level advice for their home projects.

## 🤝 How to Contribute

### Types of Contributions

We welcome various types of contributions:

- **🐛 Bug Reports** - Help us identify and fix issues
- **✨ Feature Requests** - Suggest new functionality
- **📝 Documentation** - Improve guides, API docs, and tutorials
- **🔧 Code Contributions** - Backend, frontend, or AI/ML improvements
- **🧪 Testing** - Write tests or improve test coverage
- **🎨 Design** - UI/UX improvements and accessibility enhancements
- **📊 Data** - Contribute to knowledge base content
- **🌍 Localization** - Help translate the application

### Getting Started

1. **Fork the Repository**
   ```bash
   git clone https://github.com/HectorTa1989/homecraft-intelligence.git
   cd homecraft-intelligence
   ```

2. **Set Up Development Environment**
   ```bash
   # Using Docker (recommended)
   docker-compose -f docker-compose.dev.yml up -d
   
   # Or local setup
   make setup-dev
   ```

3. **Create a Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   # or
   git checkout -b bugfix/issue-number
   ```

## 📋 Development Guidelines

### Code Style

**Python (Backend)**
- Follow **PEP 8** style guide
- Use **Black** for code formatting: `black app/`
- Use **isort** for import sorting: `isort app/`
- Use **mypy** for type checking: `mypy app/`
- Maximum line length: 88 characters

**TypeScript (Frontend)**
- Follow **Prettier** configuration
- Use **ESLint** for linting: `npm run lint`
- Use **TypeScript strict mode**
- Prefer functional components with hooks

**General Principles**
- Write self-documenting code with clear variable names
- Add docstrings for all public functions and classes
- Include type hints for Python code
- Write comprehensive tests for new features

### Commit Messages

Use **Conventional Commits** format:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

**Examples:**
```bash
feat(rag): add safety validation to query responses
fix(auth): resolve token refresh issue
docs(api): update authentication endpoint documentation
test(projects): add unit tests for project creation
```

### Testing Requirements

**Backend Tests**
```bash
# Run all tests
pytest -v --cov=app tests/

# Run specific test file
pytest tests/test_rag.py -v

# Run with coverage report
pytest --cov=app --cov-report=html tests/
```

**Frontend Tests**
```bash
# Unit tests
npm test

# E2E tests
npm run test:e2e

# Component tests
npm run test:component
```

**Test Coverage Requirements**
- **Minimum 80% coverage** for new code
- **100% coverage** for critical safety features
- All public APIs must have tests
- Include both positive and negative test cases

### Documentation Standards

- Update relevant documentation for any changes
- Include code examples in API documentation
- Write clear, concise comments for complex logic
- Update README.md if adding new features or changing setup

## 🚀 Pull Request Process

### Before Submitting

1. **Ensure all tests pass**
   ```bash
   make test-all
   ```

2. **Run linting and formatting**
   ```bash
   make lint-fix
   ```

3. **Update documentation** if needed

4. **Test your changes locally**
   ```bash
   make test-local
   ```

### Pull Request Template

When creating a PR, please include:

```markdown
## Description
Brief description of changes made.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed
- [ ] New tests added for new functionality

## Checklist
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No new warnings or errors introduced

## Screenshots (if applicable)
Add screenshots for UI changes.

## Related Issues
Closes #(issue number)
```

### Review Process

1. **Automated Checks** - CI/CD pipeline runs tests and linting
2. **Code Review** - At least one maintainer reviews the code
3. **Testing** - Changes are tested in staging environment
4. **Approval** - PR is approved and merged by maintainer

## 🐛 Bug Reports

### Before Reporting

1. **Search existing issues** to avoid duplicates
2. **Test with latest version** to ensure bug still exists
3. **Gather relevant information** (OS, browser, versions, etc.)

### Bug Report Template

```markdown
**Bug Description**
A clear and concise description of the bug.

**Steps to Reproduce**
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected Behavior**
What you expected to happen.

**Actual Behavior**
What actually happened.

**Screenshots**
If applicable, add screenshots.

**Environment**
- OS: [e.g. Windows 10, macOS 12.0]
- Browser: [e.g. Chrome 96, Firefox 95]
- Version: [e.g. 1.2.3]

**Additional Context**
Any other context about the problem.
```

## ✨ Feature Requests

### Feature Request Template

```markdown
**Feature Description**
A clear and concise description of the feature.

**Problem Statement**
What problem does this feature solve?

**Proposed Solution**
Describe your proposed solution.

**Alternatives Considered**
Other solutions you've considered.

**Additional Context**
Any other context, mockups, or examples.

**Priority**
- [ ] Low
- [ ] Medium
- [ ] High
- [ ] Critical
```

## 🏗️ Architecture Decisions

For significant architectural changes:

1. **Create an RFC** (Request for Comments) in `docs/rfcs/`
2. **Discuss in GitHub Discussions** before implementation
3. **Get approval** from core maintainers
4. **Update architecture documentation**

## 📊 Knowledge Base Contributions

### Adding Content

1. **Content Guidelines**
   - Focus on safety-first approaches
   - Include skill level requirements
   - Provide clear step-by-step instructions
   - Add relevant images or diagrams

2. **Content Structure**
   ```markdown
   # Title
   
   **Skill Level:** Beginner/Intermediate/Advanced
   **Estimated Time:** X hours
   **Safety Rating:** 1-10
   
   ## Overview
   Brief description
   
   ## Materials Needed
   - List of materials
   
   ## Tools Required
   - List of tools
   
   ## Safety Precautions
   - Safety warnings and requirements
   
   ## Step-by-Step Instructions
   1. Detailed steps
   
   ## Common Mistakes
   - Things to avoid
   
   ## Troubleshooting
   - Common issues and solutions
   ```

## 🌍 Internationalization

We welcome translations! To contribute:

1. **Check existing translations** in `frontend/src/locales/`
2. **Create new locale file** following existing structure
3. **Test translations** in the application
4. **Submit PR** with translation updates

## 🎖️ Recognition

Contributors are recognized in:
- **README.md** contributors section
- **Release notes** for significant contributions
- **Hall of Fame** on project website
- **Special badges** for different contribution types

## 📞 Getting Help

- **GitHub Discussions** - General questions and ideas
- **Discord Server** - Real-time chat with community
- **Email** - <EMAIL>
- **Office Hours** - Weekly video calls with maintainers

## 📜 Code of Conduct

This project follows the [Contributor Covenant Code of Conduct](CODE_OF_CONDUCT.md). By participating, you agree to uphold this code.

## 📄 License

By contributing to HomeCraft Intelligence, you agree that your contributions will be licensed under the MIT License.

---

**Thank you for contributing to HomeCraft Intelligence! 🏠🔧**
