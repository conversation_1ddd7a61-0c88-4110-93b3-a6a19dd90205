"""
HomeCraft Intelligence - Project Models

This module defines the database models for DIY projects, including
project details, materials, tasks, and progress tracking.

Author: HomeCraft Intelligence Team
License: MIT
"""

from sqlalchemy import Column, String, Text, Integer, Float, Boolean, DateTime, Foreign<PERSON>ey, JSON, Enum
from sqlalchemy.dialects.postgresql import UUID, ARRA<PERSON>
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
import enum
from datetime import datetime
from typing import Optional, List, Dict, Any

from app.database import Base


class ProjectStatus(str, enum.Enum):
    """Project status enumeration."""
    PLANNING = "planning"
    IN_PROGRESS = "in_progress"
    ON_HOLD = "on_hold"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class ProjectType(str, enum.Enum):
    """Project type enumeration."""
    FLOORING = "flooring"
    PAINTING = "painting"
    PLUMBING = "plumbing"
    ELECTRICAL = "electrical"
    ROOFING = "roofing"
    KITCHEN = "kitchen"
    BATHROOM = "bathroom"
    LANDSCAPING = "landscaping"
    HVAC = "hvac"
    STRUCTURAL = "structural"
    OTHER = "other"


class DifficultyLevel(str, enum.Enum):
    """Project difficulty level enumeration."""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    PROFESSIONAL = "professional"


class TaskStatus(str, enum.Enum):
    """Task status enumeration."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    SKIPPED = "skipped"


class Project(Base):
    """
    Project model representing a DIY home improvement project.
    
    This model stores comprehensive project information including
    planning details, progress tracking, safety assessments, and costs.
    """
    __tablename__ = "projects"

    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    
    # Basic project information
    title = Column(String(200), nullable=False, index=True)
    description = Column(Text)
    project_type = Column(Enum(ProjectType), nullable=False, index=True)
    difficulty_level = Column(Enum(DifficultyLevel), nullable=False)
    status = Column(Enum(ProjectStatus), default=ProjectStatus.PLANNING, index=True)
    
    # Progress tracking
    progress_percentage = Column(Float, default=0.0)
    estimated_duration_hours = Column(Integer)
    actual_duration_hours = Column(Integer)
    
    # Dates
    start_date = Column(DateTime(timezone=True))
    target_completion_date = Column(DateTime(timezone=True))
    actual_completion_date = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Location and dimensions
    location = Column(String(200))  # Room or area
    dimensions = Column(JSON)  # Flexible storage for measurements
    
    # Cost tracking
    estimated_cost = Column(Float)
    actual_cost = Column(Float, default=0.0)
    budget_limit = Column(Float)
    cost_breakdown = Column(JSON)  # Detailed cost analysis
    
    # Safety and compliance
    safety_score = Column(Float)
    safety_warnings = Column(ARRAY(Text))
    building_permits_required = Column(Boolean, default=False)
    professional_help_recommended = Column(Boolean, default=False)
    
    # AI-generated content
    ai_recommendations = Column(JSON)
    generated_plan = Column(JSON)
    material_suggestions = Column(JSON)
    
    # Project metadata
    tags = Column(ARRAY(String))
    images = Column(ARRAY(String))  # URLs to project images
    documents = Column(ARRAY(String))  # URLs to project documents
    
    # Sharing and visibility
    is_public = Column(Boolean, default=False)
    is_template = Column(Boolean, default=False)
    template_category = Column(String(100))
    
    # Analytics
    view_count = Column(Integer, default=0)
    like_count = Column(Integer, default=0)
    
    # Relationships
    user = relationship("User", back_populates="projects")
    tasks = relationship("ProjectTask", back_populates="project", cascade="all, delete-orphan")
    materials = relationship("ProjectMaterial", back_populates="project", cascade="all, delete-orphan")
    progress_logs = relationship("ProjectProgressLog", back_populates="project", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Project(id={self.id}, title='{self.title}', status='{self.status}')>"
    
    @property
    def is_completed(self) -> bool:
        """Check if project is completed."""
        return self.status == ProjectStatus.COMPLETED
    
    @property
    def is_overdue(self) -> bool:
        """Check if project is overdue."""
        if not self.target_completion_date:
            return False
        return datetime.utcnow() > self.target_completion_date and not self.is_completed
    
    @property
    def completion_rate(self) -> float:
        """Calculate completion rate based on completed tasks."""
        if not self.tasks:
            return 0.0
        
        completed_tasks = sum(1 for task in self.tasks if task.status == TaskStatus.COMPLETED)
        return (completed_tasks / len(self.tasks)) * 100
    
    @property
    def cost_variance(self) -> Optional[float]:
        """Calculate cost variance (actual vs estimated)."""
        if not self.estimated_cost:
            return None
        return self.actual_cost - self.estimated_cost
    
    @property
    def cost_variance_percentage(self) -> Optional[float]:
        """Calculate cost variance as percentage."""
        if not self.estimated_cost or self.estimated_cost == 0:
            return None
        return ((self.actual_cost - self.estimated_cost) / self.estimated_cost) * 100


class ProjectTask(Base):
    """
    Project task model representing individual tasks within a project.
    
    Tasks break down projects into manageable steps with dependencies,
    time estimates, and completion tracking.
    """
    __tablename__ = "project_tasks"

    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False, index=True)
    
    # Task information
    title = Column(String(200), nullable=False)
    description = Column(Text)
    order_index = Column(Integer, nullable=False)
    status = Column(Enum(TaskStatus), default=TaskStatus.NOT_STARTED, index=True)
    
    # Time tracking
    estimated_duration_hours = Column(Float)
    actual_duration_hours = Column(Float)
    start_date = Column(DateTime(timezone=True))
    completion_date = Column(DateTime(timezone=True))
    
    # Dependencies
    depends_on_task_ids = Column(ARRAY(UUID))
    
    # Task metadata
    difficulty_level = Column(Enum(DifficultyLevel))
    required_tools = Column(ARRAY(String))
    required_materials = Column(JSON)
    safety_notes = Column(Text)
    
    # AI-generated content
    ai_instructions = Column(Text)
    ai_tips = Column(ARRAY(Text))
    
    # Progress tracking
    progress_percentage = Column(Float, default=0.0)
    notes = Column(Text)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    project = relationship("Project", back_populates="tasks")
    
    def __repr__(self):
        return f"<ProjectTask(id={self.id}, title='{self.title}', status='{self.status}')>"
    
    @property
    def is_ready_to_start(self) -> bool:
        """Check if task dependencies are completed."""
        if not self.depends_on_task_ids:
            return True
        
        # Check if all dependent tasks are completed
        for task in self.project.tasks:
            if task.id in self.depends_on_task_ids and task.status != TaskStatus.COMPLETED:
                return False
        
        return True


class ProjectMaterial(Base):
    """
    Project material model representing materials needed for a project.
    
    Tracks material requirements, costs, quantities, and purchase status.
    """
    __tablename__ = "project_materials"

    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False, index=True)
    
    # Material information
    name = Column(String(200), nullable=False)
    description = Column(Text)
    category = Column(String(100), index=True)
    brand = Column(String(100))
    model_number = Column(String(100))
    
    # Quantity and units
    quantity_needed = Column(Float, nullable=False)
    quantity_purchased = Column(Float, default=0.0)
    unit = Column(String(50), nullable=False)  # sq ft, linear ft, each, etc.
    
    # Cost information
    estimated_unit_cost = Column(Float)
    actual_unit_cost = Column(Float)
    total_estimated_cost = Column(Float)
    total_actual_cost = Column(Float, default=0.0)
    
    # Purchase tracking
    is_purchased = Column(Boolean, default=False)
    purchase_date = Column(DateTime(timezone=True))
    supplier = Column(String(200))
    supplier_url = Column(String(500))
    
    # Material specifications
    specifications = Column(JSON)
    alternatives = Column(JSON)  # Alternative materials
    
    # AI recommendations
    ai_recommended = Column(Boolean, default=False)
    ai_reasoning = Column(Text)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    project = relationship("Project", back_populates="materials")
    
    def __repr__(self):
        return f"<ProjectMaterial(id={self.id}, name='{self.name}', quantity={self.quantity_needed})>"
    
    @property
    def quantity_remaining(self) -> float:
        """Calculate remaining quantity needed."""
        return max(0, self.quantity_needed - self.quantity_purchased)
    
    @property
    def cost_variance(self) -> Optional[float]:
        """Calculate cost variance for this material."""
        if not self.estimated_unit_cost or not self.actual_unit_cost:
            return None
        return (self.actual_unit_cost - self.estimated_unit_cost) * self.quantity_purchased


class ProjectProgressLog(Base):
    """
    Project progress log model for tracking project updates and milestones.
    
    Maintains a chronological record of project progress, updates, and notes.
    """
    __tablename__ = "project_progress_logs"

    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False, index=True)
    
    # Log entry information
    title = Column(String(200))
    description = Column(Text, nullable=False)
    progress_percentage = Column(Float)
    
    # Media attachments
    images = Column(ARRAY(String))
    videos = Column(ARRAY(String))
    documents = Column(ARRAY(String))
    
    # Time tracking
    hours_worked = Column(Float)
    
    # Metadata
    tags = Column(ARRAY(String))
    is_milestone = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # Relationships
    project = relationship("Project", back_populates="progress_logs")
    
    def __repr__(self):
        return f"<ProjectProgressLog(id={self.id}, project_id={self.project_id})>"
