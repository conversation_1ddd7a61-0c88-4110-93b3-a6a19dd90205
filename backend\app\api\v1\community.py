"""
HomeCraft Intelligence - Community API Routes

This module defines the API endpoints for community features,
including project sharing, forums, and user interactions.

Author: HomeCraft Intelligence Team
License: MIT
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field
import logging
from datetime import datetime

from app.database import get_db
from app.services.auth_service import auth_service
from app.models.user import User
from app.core.exceptions import NotFoundError, ValidationError
from app.middleware.rate_limit import rate_limit


router = APIRouter()
security = HTTPBearer()


# Schemas
class CommunityPostCreate(BaseModel):
    """Schema for creating community posts."""
    title: str = Field(..., min_length=5, max_length=200)
    content: str = Field(..., min_length=10, max_length=5000)
    category: str = Field(..., description="Post category")
    tags: Optional[List[str]] = Field(None, max_items=10)
    project_id: Optional[str] = Field(None, description="Associated project ID")
    images: Optional[List[str]] = Field(None, max_items=10)


class CommunityPostResponse(BaseModel):
    """Schema for community post responses."""
    id: str
    title: str
    content: str
    category: str
    tags: List[str]
    author: Dict[str, Any]
    project_id: Optional[str]
    images: List[str]
    likes_count: int
    comments_count: int
    views_count: int
    is_featured: bool
    created_at: datetime
    updated_at: datetime


class CommentCreate(BaseModel):
    """Schema for creating comments."""
    content: str = Field(..., min_length=1, max_length=1000)
    parent_comment_id: Optional[str] = Field(None, description="Parent comment for replies")


class CommentResponse(BaseModel):
    """Schema for comment responses."""
    id: str
    content: str
    author: Dict[str, Any]
    parent_comment_id: Optional[str]
    likes_count: int
    replies_count: int
    created_at: datetime
    updated_at: datetime


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """Dependency to get current authenticated user."""
    try:
        access_token = credentials.credentials
        return await auth_service.get_current_user(db, access_token)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )


@router.get("/posts", response_model=List[CommunityPostResponse])
@rate_limit(requests_per_minute=30, requests_per_hour=200)
async def get_community_posts(
    category: Optional[str] = Query(None, description="Filter by category"),
    tag: Optional[str] = Query(None, description="Filter by tag"),
    featured: Optional[bool] = Query(None, description="Filter featured posts"),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get community posts with filtering and pagination.
    
    Returns a list of community posts with author information,
    engagement metrics, and associated project data.
    """
    try:
        # Mock community posts data
        # In production, this would query a posts table with proper filtering
        mock_posts = [
            {
                "id": "post_1",
                "title": "My Kitchen Renovation Journey - Before & After",
                "content": "Just finished my kitchen renovation project! Here's what I learned...",
                "category": "kitchen",
                "tags": ["renovation", "kitchen", "diy", "before-after"],
                "author": {
                    "id": str(current_user.id),
                    "name": f"{current_user.first_name} {current_user.last_name}",
                    "username": current_user.username,
                    "avatar_url": current_user.avatar_url,
                    "skill_level": current_user.skill_level.value
                },
                "project_id": "proj_123",
                "images": [
                    "https://cdn.homecraft.com/posts/post_1_img_1.jpg",
                    "https://cdn.homecraft.com/posts/post_1_img_2.jpg"
                ],
                "likes_count": 45,
                "comments_count": 12,
                "views_count": 234,
                "is_featured": True,
                "created_at": datetime(2024, 1, 15, 10, 30),
                "updated_at": datetime(2024, 1, 15, 10, 30)
            },
            {
                "id": "post_2",
                "title": "Safety Tips for Electrical Work",
                "content": "Here are some essential safety tips I've learned from my electrical projects...",
                "category": "electrical",
                "tags": ["safety", "electrical", "tips", "beginner"],
                "author": {
                    "id": "user_456",
                    "name": "John Expert",
                    "username": "john_expert",
                    "avatar_url": "https://cdn.homecraft.com/avatars/user_456.jpg",
                    "skill_level": "advanced"
                },
                "project_id": None,
                "images": [],
                "likes_count": 78,
                "comments_count": 23,
                "views_count": 567,
                "is_featured": False,
                "created_at": datetime(2024, 1, 14, 15, 45),
                "updated_at": datetime(2024, 1, 14, 15, 45)
            }
        ]
        
        # Apply filters
        filtered_posts = mock_posts
        
        if category:
            filtered_posts = [p for p in filtered_posts if p["category"] == category]
        
        if tag:
            filtered_posts = [p for p in filtered_posts if tag in p["tags"]]
        
        if featured is not None:
            filtered_posts = [p for p in filtered_posts if p["is_featured"] == featured]
        
        # Apply pagination
        paginated_posts = filtered_posts[skip:skip + limit]
        
        logging.info(f"Community posts retrieved for user {current_user.id}")
        
        return [CommunityPostResponse(**post) for post in paginated_posts]
        
    except Exception as e:
        logging.error(f"Community posts retrieval failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve community posts"
        )


@router.post("/posts", response_model=CommunityPostResponse)
@rate_limit(requests_per_minute=5, requests_per_hour=20)
async def create_community_post(
    post_data: CommunityPostCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new community post.
    
    Allows users to share their projects, ask questions,
    and contribute to the community knowledge base.
    """
    try:
        # Validate category
        valid_categories = [
            "kitchen", "bathroom", "electrical", "plumbing", "flooring",
            "painting", "roofing", "landscaping", "general", "showcase"
        ]
        
        if post_data.category not in valid_categories:
            raise ValidationError(f"Invalid category. Must be one of: {valid_categories}")
        
        # In production, this would create a new post record
        new_post = {
            "id": f"post_{datetime.now().timestamp()}",
            "title": post_data.title,
            "content": post_data.content,
            "category": post_data.category,
            "tags": post_data.tags or [],
            "author": {
                "id": str(current_user.id),
                "name": f"{current_user.first_name} {current_user.last_name}",
                "username": current_user.username,
                "avatar_url": current_user.avatar_url,
                "skill_level": current_user.skill_level.value
            },
            "project_id": post_data.project_id,
            "images": post_data.images or [],
            "likes_count": 0,
            "comments_count": 0,
            "views_count": 0,
            "is_featured": False,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        logging.info(f"Community post created by user {current_user.id}")
        
        return CommunityPostResponse(**new_post)
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        logging.error(f"Community post creation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create community post"
        )


@router.get("/posts/{post_id}", response_model=CommunityPostResponse)
@rate_limit(requests_per_minute=60, requests_per_hour=300)
async def get_community_post(
    post_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get a specific community post by ID.
    
    Returns detailed post information including content,
    author details, and engagement metrics.
    """
    try:
        # Mock post retrieval
        # In production, this would query the database
        mock_post = {
            "id": post_id,
            "title": "Detailed Kitchen Renovation Guide",
            "content": "Here's my complete guide to renovating a kitchen on a budget...",
            "category": "kitchen",
            "tags": ["renovation", "kitchen", "budget", "guide"],
            "author": {
                "id": str(current_user.id),
                "name": f"{current_user.first_name} {current_user.last_name}",
                "username": current_user.username,
                "avatar_url": current_user.avatar_url,
                "skill_level": current_user.skill_level.value
            },
            "project_id": "proj_123",
            "images": ["https://cdn.homecraft.com/posts/detailed_guide.jpg"],
            "likes_count": 156,
            "comments_count": 34,
            "views_count": 1234,
            "is_featured": True,
            "created_at": datetime(2024, 1, 10, 14, 20),
            "updated_at": datetime(2024, 1, 12, 16, 45)
        }
        
        logging.info(f"Community post {post_id} retrieved by user {current_user.id}")
        
        return CommunityPostResponse(**mock_post)
        
    except Exception as e:
        logging.error(f"Community post retrieval failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve community post"
        )


@router.post("/posts/{post_id}/like")
@rate_limit(requests_per_minute=30, requests_per_hour=100)
async def like_community_post(
    post_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Like or unlike a community post.
    
    Toggles the like status for the current user on the specified post.
    """
    try:
        # In production, this would:
        # 1. Check if user already liked the post
        # 2. Toggle like status
        # 3. Update like count
        # 4. Send notification to post author
        
        # Mock implementation
        is_liked = True  # Assume user is now liking the post
        new_likes_count = 157  # Mock updated count
        
        logging.info(f"Post {post_id} liked by user {current_user.id}")
        
        return {
            "post_id": post_id,
            "is_liked": is_liked,
            "likes_count": new_likes_count,
            "message": "Post liked successfully" if is_liked else "Post unliked successfully"
        }
        
    except Exception as e:
        logging.error(f"Post like operation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to like post"
        )


@router.get("/posts/{post_id}/comments", response_model=List[CommentResponse])
@rate_limit(requests_per_minute=40, requests_per_hour=200)
async def get_post_comments(
    post_id: str,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get comments for a specific post.
    
    Returns paginated list of comments with author information
    and reply threading support.
    """
    try:
        # Mock comments data
        mock_comments = [
            {
                "id": "comment_1",
                "content": "Great project! I'm planning something similar. What was your total budget?",
                "author": {
                    "id": "user_789",
                    "name": "Sarah DIYer",
                    "username": "sarah_diyer",
                    "avatar_url": "https://cdn.homecraft.com/avatars/user_789.jpg",
                    "skill_level": "intermediate"
                },
                "parent_comment_id": None,
                "likes_count": 5,
                "replies_count": 2,
                "created_at": datetime(2024, 1, 15, 11, 30),
                "updated_at": datetime(2024, 1, 15, 11, 30)
            },
            {
                "id": "comment_2",
                "content": "Thanks! My total budget was around $3,500. Most of that went to the countertops.",
                "author": {
                    "id": str(current_user.id),
                    "name": f"{current_user.first_name} {current_user.last_name}",
                    "username": current_user.username,
                    "avatar_url": current_user.avatar_url,
                    "skill_level": current_user.skill_level.value
                },
                "parent_comment_id": "comment_1",
                "likes_count": 3,
                "replies_count": 0,
                "created_at": datetime(2024, 1, 15, 12, 15),
                "updated_at": datetime(2024, 1, 15, 12, 15)
            }
        ]
        
        # Apply pagination
        paginated_comments = mock_comments[skip:skip + limit]
        
        logging.info(f"Comments for post {post_id} retrieved by user {current_user.id}")
        
        return [CommentResponse(**comment) for comment in paginated_comments]
        
    except Exception as e:
        logging.error(f"Post comments retrieval failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve post comments"
        )


@router.post("/posts/{post_id}/comments", response_model=CommentResponse)
@rate_limit(requests_per_minute=10, requests_per_hour=50)
async def create_comment(
    post_id: str,
    comment_data: CommentCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new comment on a post.
    
    Allows users to comment on posts and reply to existing comments.
    """
    try:
        # In production, this would create a new comment record
        new_comment = {
            "id": f"comment_{datetime.now().timestamp()}",
            "content": comment_data.content,
            "author": {
                "id": str(current_user.id),
                "name": f"{current_user.first_name} {current_user.last_name}",
                "username": current_user.username,
                "avatar_url": current_user.avatar_url,
                "skill_level": current_user.skill_level.value
            },
            "parent_comment_id": comment_data.parent_comment_id,
            "likes_count": 0,
            "replies_count": 0,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        logging.info(f"Comment created on post {post_id} by user {current_user.id}")
        
        return CommentResponse(**new_comment)
        
    except Exception as e:
        logging.error(f"Comment creation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create comment"
        )


@router.get("/categories")
@rate_limit(requests_per_minute=30, requests_per_hour=150)
async def get_community_categories(
    current_user: User = Depends(get_current_user)
):
    """
    Get available community categories.
    
    Returns a list of all available post categories with
    descriptions and post counts.
    """
    try:
        categories = [
            {
                "id": "kitchen",
                "name": "Kitchen",
                "description": "Kitchen renovations, appliances, and design",
                "post_count": 234,
                "icon": "🍳"
            },
            {
                "id": "bathroom",
                "name": "Bathroom",
                "description": "Bathroom remodeling and fixtures",
                "post_count": 156,
                "icon": "🚿"
            },
            {
                "id": "electrical",
                "name": "Electrical",
                "description": "Electrical work, wiring, and safety",
                "post_count": 189,
                "icon": "⚡"
            },
            {
                "id": "plumbing",
                "name": "Plumbing",
                "description": "Plumbing repairs and installations",
                "post_count": 145,
                "icon": "🔧"
            },
            {
                "id": "flooring",
                "name": "Flooring",
                "description": "Floor installation and refinishing",
                "post_count": 198,
                "icon": "🏠"
            },
            {
                "id": "painting",
                "name": "Painting",
                "description": "Interior and exterior painting projects",
                "post_count": 267,
                "icon": "🎨"
            },
            {
                "id": "showcase",
                "name": "Project Showcase",
                "description": "Show off your completed projects",
                "post_count": 345,
                "icon": "✨"
            },
            {
                "id": "general",
                "name": "General Discussion",
                "description": "General DIY discussion and questions",
                "post_count": 423,
                "icon": "💬"
            }
        ]
        
        return {
            "categories": categories,
            "total_categories": len(categories),
            "total_posts": sum(cat["post_count"] for cat in categories)
        }
        
    except Exception as e:
        logging.error(f"Community categories retrieval failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve community categories"
        )
