"""
HomeCraft Intelligence - Projects API Routes

This module defines the API endpoints for project management,
including CRUD operations, progress tracking, and AI-powered features.

Author: HomeCraft Intelligence Team
License: MIT
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from sqlalchemy.orm import selectinload
import logging
from datetime import datetime

from app.database import get_db
from app.services.auth_service import auth_service
from app.services.rag_service import rag_service
from app.services.material_service import material_service
from app.services.safety_service import safety_service
from app.models.user import User
from app.models.project import Project, ProjectTask, ProjectMaterial, ProjectStatus, ProjectType, DifficultyLevel
from app.schemas.project import (
    ProjectCreate, ProjectUpdate, ProjectResponse, ProjectListResponse,
    ProjectTaskCreate, ProjectTaskUpdate, ProjectTaskResponse,
    ProjectMaterialCreate, ProjectMaterialResponse,
    ProjectProgressLogCreate, ProjectProgressLogResponse
)
from app.core.exceptions import NotFoundError, AuthorizationError, ValidationError
from app.middleware.rate_limit import rate_limit


router = APIRouter()
security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """Dependency to get current authenticated user."""
    try:
        access_token = credentials.credentials
        return await auth_service.get_current_user(db, access_token)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )


@router.post("/", response_model=ProjectResponse)
@rate_limit(requests_per_minute=10, requests_per_hour=50)
async def create_project(
    project_data: ProjectCreate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new DIY project with AI-powered planning and safety assessment.
    
    This endpoint creates a comprehensive project plan including:
    - Safety assessment and compliance checking
    - Material calculations and cost estimates
    - Task breakdown and timeline
    - AI-generated recommendations
    """
    try:
        # Create project instance
        project = Project(
            user_id=current_user.id,
            title=project_data.title,
            description=project_data.description,
            project_type=project_data.project_type,
            difficulty_level=project_data.difficulty_level,
            location=project_data.location,
            dimensions=project_data.dimensions.dict() if project_data.dimensions else None,
            estimated_cost=project_data.estimated_cost,
            budget_limit=project_data.budget_limit,
            target_completion_date=project_data.target_completion_date,
            tags=project_data.tags or []
        )
        
        db.add(project)
        await db.flush()  # Get project ID
        
        # Generate AI-powered project plan in background
        background_tasks.add_task(
            generate_project_plan,
            str(project.id),
            project_data.dict(),
            current_user.skill_level.value
        )
        
        await db.commit()
        await db.refresh(project)
        
        logging.info(f"Project created: {project.id} by user {current_user.id}")
        
        return ProjectResponse.from_orm(project)
        
    except Exception as e:
        await db.rollback()
        logging.error(f"Project creation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create project"
        )


@router.get("/", response_model=ProjectListResponse)
@rate_limit(requests_per_minute=30, requests_per_hour=200)
async def list_projects(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status_filter: Optional[ProjectStatus] = Query(None),
    project_type: Optional[ProjectType] = Query(None),
    search: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    List user's projects with filtering and pagination.
    
    Supports filtering by status, project type, and text search.
    Returns paginated results with project summaries.
    """
    try:
        # Build query
        query = select(Project).where(Project.user_id == current_user.id)
        
        # Apply filters
        if status_filter:
            query = query.where(Project.status == status_filter)
        
        if project_type:
            query = query.where(Project.project_type == project_type)
        
        if search:
            search_term = f"%{search}%"
            query = query.where(
                or_(
                    Project.title.ilike(search_term),
                    Project.description.ilike(search_term)
                )
            )
        
        # Get total count
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # Apply pagination and ordering
        query = query.order_by(Project.updated_at.desc()).offset(skip).limit(limit)
        
        # Execute query with relationships
        query = query.options(
            selectinload(Project.tasks),
            selectinload(Project.materials)
        )
        
        result = await db.execute(query)
        projects = result.scalars().all()
        
        return ProjectListResponse(
            projects=[ProjectResponse.from_orm(project) for project in projects],
            total=total,
            skip=skip,
            limit=limit
        )
        
    except Exception as e:
        logging.error(f"Project listing failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve projects"
        )


@router.get("/{project_id}", response_model=ProjectResponse)
@rate_limit(requests_per_minute=60, requests_per_hour=300)
async def get_project(
    project_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get detailed project information including tasks, materials, and progress.
    
    Returns comprehensive project data with all related entities.
    """
    try:
        # Query with all relationships
        query = select(Project).where(
            and_(
                Project.id == project_id,
                Project.user_id == current_user.id
            )
        ).options(
            selectinload(Project.tasks),
            selectinload(Project.materials),
            selectinload(Project.progress_logs)
        )
        
        result = await db.execute(query)
        project = result.scalar_one_or_none()
        
        if not project:
            raise NotFoundError("Project not found")
        
        # Increment view count
        project.view_count += 1
        await db.commit()
        
        return ProjectResponse.from_orm(project)
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    except Exception as e:
        logging.error(f"Project retrieval failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve project"
        )


@router.put("/{project_id}", response_model=ProjectResponse)
@rate_limit(requests_per_minute=20, requests_per_hour=100)
async def update_project(
    project_id: str,
    project_data: ProjectUpdate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update project information and regenerate AI recommendations if needed.
    
    Updates project details and triggers background tasks for
    recalculating safety scores and material estimates if relevant fields changed.
    """
    try:
        # Get existing project
        query = select(Project).where(
            and_(
                Project.id == project_id,
                Project.user_id == current_user.id
            )
        )
        
        result = await db.execute(query)
        project = result.scalar_one_or_none()
        
        if not project:
            raise NotFoundError("Project not found")
        
        # Track significant changes
        significant_changes = False
        
        # Update fields
        update_data = project_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(project, field):
                old_value = getattr(project, field)
                setattr(project, field, value)
                
                # Check for changes that require recalculation
                if field in ['project_type', 'dimensions', 'difficulty_level']:
                    significant_changes = True
        
        project.updated_at = datetime.utcnow()
        
        # Regenerate recommendations if significant changes
        if significant_changes:
            background_tasks.add_task(
                update_project_recommendations,
                str(project.id),
                current_user.skill_level.value
            )
        
        await db.commit()
        await db.refresh(project)
        
        logging.info(f"Project updated: {project.id}")
        
        return ProjectResponse.from_orm(project)
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    except Exception as e:
        await db.rollback()
        logging.error(f"Project update failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update project"
        )


@router.delete("/{project_id}")
@rate_limit(requests_per_minute=10, requests_per_hour=30)
async def delete_project(
    project_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete a project and all associated data.
    
    Permanently removes the project and all related tasks, materials,
    and progress logs. This action cannot be undone.
    """
    try:
        # Get project
        query = select(Project).where(
            and_(
                Project.id == project_id,
                Project.user_id == current_user.id
            )
        )
        
        result = await db.execute(query)
        project = result.scalar_one_or_none()
        
        if not project:
            raise NotFoundError("Project not found")
        
        # Delete project (cascade will handle related records)
        await db.delete(project)
        await db.commit()
        
        logging.info(f"Project deleted: {project_id} by user {current_user.id}")
        
        return {"message": "Project deleted successfully"}
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    except Exception as e:
        await db.rollback()
        logging.error(f"Project deletion failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete project"
        )


@router.post("/{project_id}/tasks", response_model=ProjectTaskResponse)
@rate_limit(requests_per_minute=20, requests_per_hour=100)
async def create_project_task(
    project_id: str,
    task_data: ProjectTaskCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new task for a project.
    
    Adds a task to the project with proper ordering and dependency management.
    """
    try:
        # Verify project ownership
        project_query = select(Project).where(
            and_(
                Project.id == project_id,
                Project.user_id == current_user.id
            )
        )
        
        project_result = await db.execute(project_query)
        project = project_result.scalar_one_or_none()
        
        if not project:
            raise NotFoundError("Project not found")
        
        # Create task
        task = ProjectTask(
            project_id=project_id,
            title=task_data.title,
            description=task_data.description,
            order_index=task_data.order_index,
            estimated_duration_hours=task_data.estimated_duration_hours,
            difficulty_level=task_data.difficulty_level,
            required_tools=task_data.required_tools or [],
            required_materials=task_data.required_materials.dict() if task_data.required_materials else None,
            safety_notes=task_data.safety_notes,
            depends_on_task_ids=task_data.depends_on_task_ids or []
        )
        
        db.add(task)
        await db.commit()
        await db.refresh(task)
        
        logging.info(f"Task created: {task.id} for project {project_id}")
        
        return ProjectTaskResponse.from_orm(task)
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    except Exception as e:
        await db.rollback()
        logging.error(f"Task creation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create task"
        )


# Background task functions
async def generate_project_plan(project_id: str, project_data: Dict[str, Any], user_skill_level: str):
    """Background task to generate AI-powered project plan."""
    try:
        # This would integrate with the RAG service to generate:
        # 1. Task breakdown
        # 2. Material calculations
        # 3. Safety assessment
        # 4. Timeline estimation
        
        logging.info(f"Generating project plan for {project_id}")
        
        # Placeholder for AI integration
        # In production, this would call the RAG service with project details
        
    except Exception as e:
        logging.error(f"Project plan generation failed for {project_id}: {e}")


async def update_project_recommendations(project_id: str, user_skill_level: str):
    """Background task to update project recommendations."""
    try:
        logging.info(f"Updating recommendations for project {project_id}")
        
        # Placeholder for recommendation updates
        # This would recalculate safety scores, material estimates, etc.
        
    except Exception as e:
        logging.error(f"Recommendation update failed for {project_id}: {e}")
