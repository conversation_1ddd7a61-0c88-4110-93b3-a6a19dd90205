"""
HomeCraft Intelligence - Minimal FastAPI Application

A simplified version of the main application for testing and development.
This version includes only the essential components to get the API running.

Author: HomeCraft Intelligence Team
License: MIT
"""

from fastapi import FastAPI, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import <PERSON><PERSON>NResponse
import logging
import time
from typing import Dict, Any

# Configure basic logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("homecraft")

# Create FastAPI application
app = FastAPI(
    title="HomeCraft Intelligence API",
    description="AI-Powered DIY Home Improvement Assistant API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH"],
    allow_headers=["*"],
)

# Health check endpoint
@app.get("/health", tags=["Health"])
async def health_check():
    """Health check endpoint for monitoring."""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "version": "1.0.0",
        "environment": "development"
    }

# Root endpoint
@app.get("/", tags=["Root"])
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Welcome to HomeCraft Intelligence API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health",
        "status": "running"
    }

# Mock authentication endpoint
@app.post("/api/v1/auth/login", tags=["Authentication"])
async def login(credentials: Dict[str, Any]):
    """Mock login endpoint."""
    email = credentials.get("email")
    password = credentials.get("password")
    
    if not email or not password:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email and password are required"
        )
    
    # Mock successful login
    return {
        "access_token": "mock_access_token_12345",
        "token_type": "bearer",
        "expires_in": 3600,
        "user": {
            "id": "user_123",
            "email": email,
            "first_name": "John",
            "last_name": "Doe",
            "skill_level": "intermediate"
        }
    }

# Mock registration endpoint
@app.post("/api/v1/auth/register", tags=["Authentication"])
async def register(user_data: Dict[str, Any]):
    """Mock registration endpoint."""
    required_fields = ["email", "password", "first_name", "last_name"]
    
    for field in required_fields:
        if not user_data.get(field):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"{field} is required"
            )
    
    # Mock successful registration
    return {
        "message": "User registered successfully",
        "user": {
            "id": "user_new_123",
            "email": user_data["email"],
            "first_name": user_data["first_name"],
            "last_name": user_data["last_name"],
            "skill_level": user_data.get("skill_level", "beginner")
        }
    }

# Mock RAG query endpoint
@app.post("/api/v1/rag/query", tags=["RAG Assistant"])
async def rag_query(query_data: Dict[str, Any]):
    """Mock RAG query endpoint."""
    query = query_data.get("query")
    
    if not query:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Query is required"
        )
    
    # Mock AI response
    return {
        "query": query,
        "response": f"This is a mock AI response to your query: '{query}'. In a real implementation, this would be generated by our AI assistant with relevant DIY advice and safety recommendations.",
        "sources": [
            {
                "title": "DIY Kitchen Renovation Guide",
                "url": "https://example.com/kitchen-guide",
                "relevance": 0.95
            },
            {
                "title": "Safety Tips for Home Improvement",
                "url": "https://example.com/safety-tips",
                "relevance": 0.87
            }
        ],
        "safety_warnings": [
            "Always turn off electricity before working with electrical components",
            "Wear appropriate safety equipment"
        ],
        "confidence": 0.92,
        "response_time": 1.2
    }

# Mock projects endpoint
@app.get("/api/v1/projects", tags=["Projects"])
async def get_projects():
    """Mock projects list endpoint."""
    return {
        "projects": [
            {
                "id": "proj_1",
                "title": "Kitchen Cabinet Installation",
                "description": "Installing new kitchen cabinets",
                "status": "in_progress",
                "progress": 65,
                "created_at": "2024-01-10T10:00:00Z"
            },
            {
                "id": "proj_2",
                "title": "Bathroom Tile Replacement",
                "description": "Replacing old bathroom tiles",
                "status": "planning",
                "progress": 15,
                "created_at": "2024-01-15T14:30:00Z"
            }
        ],
        "total": 2
    }

# Mock materials calculation endpoint
@app.post("/api/v1/materials/calculate", tags=["Materials"])
async def calculate_materials(calculation_data: Dict[str, Any]):
    """Mock materials calculation endpoint."""
    project_type = calculation_data.get("project_type")
    dimensions = calculation_data.get("dimensions", {})
    
    if not project_type:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Project type is required"
        )
    
    # Mock calculation result
    return {
        "project_type": project_type,
        "material_type": "hardwood_flooring",
        "base_quantity": 250.0,
        "waste_allowance": 25.0,
        "total_quantity": 275.0,
        "units_needed": 28,
        "unit_type": "sq_ft",
        "material_cost": 1375.0,
        "waste_cost": 125.0,
        "total_estimated_cost": 1500.0,
        "tools_required": ["saw", "hammer", "measuring_tape", "spacers"],
        "calculation_timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ")
    }

# Mock safety assessment endpoint
@app.post("/api/v1/safety/assess", tags=["Safety"])
async def assess_safety(assessment_data: Dict[str, Any]):
    """Mock safety assessment endpoint."""
    project_description = assessment_data.get("project_description")
    project_type = assessment_data.get("project_type")
    
    if not project_description or not project_type:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Project description and type are required"
        )
    
    # Mock safety assessment
    return {
        "safety_score": 85.0,
        "risk_level": "medium",
        "hazards_identified": [
            {
                "hazard": "Electrical shock risk",
                "severity": "high",
                "mitigation": "Turn off power at breaker"
            }
        ],
        "safety_warnings": [
            "Always wear safety glasses",
            "Use proper ventilation",
            "Keep first aid kit nearby"
        ],
        "professional_required": False,
        "permit_required": False,
        "assessment_timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ")
    }

# Exception handler
@app.exception_handler(500)
async def internal_server_error_handler(request, exc):
    """Handle internal server errors."""
    logger.error(f"Internal server error: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "detail": "Internal server error",
            "error_id": str(time.time())
        }
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.minimal_main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
